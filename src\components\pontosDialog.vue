<template>
  <v-dialog
    v-model="visibleProxy"
    max-width="500"
    @update:modelValue="close"
    class="rule-dialog-mask"
  >
    <v-card class="rule-dialog-box">
      <div class="rule-dialog-content">
        <v-table class="pontos-table" height="300px" fixed-header>
          <thead>
            <tr>
              <th>Registro</th>
              <th>Pontos</th>
              <th>Total de pontos</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!items.length">
              <td colspan="3" style="text-align: center">Nenhum dado</td>
            </tr>
            <tr v-for="item in items" :key="item.type">
              <td>{{ typeMap[item.type] }}</td>
              <td>{{ item.points }}</td>
              <td>{{ item.final_total_points }}</td>
            </tr>
          </tbody>
        </v-table>
      </div>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { getUserPointsDetail } from "@/api/jackpot";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
});
const emit = defineEmits(["update:visible"]);

// v-model 兼容
const visibleProxy = computed({
  get: () => props.visible,
  set: (v) => emit("update:visible", v),
});
const typeMap = {
  1: "Recarregue",
  2: "Aposte",
  3: "Apostas",
  4: "Vencendo",
  5: "Registrar",
  6: "Recarga de usuário",
};
// 数据状态
const items = ref([]);
const loading = ref(false);

// 监听弹窗打开状态，每次打开时请求数据
watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      await fetchPointsData();
    }
  }
);

// 获取积分详情数据
async function fetchPointsData() {
  try {
    loading.value = true;
    const response = await getUserPointsDetail();
    console.log(response);
    // 根据接口返回的数据结构进行处理
    // 这里假设接口返回的数据格式为数组，包含 registro, pontos, total 字段
    // 如果接口返回的数据结构不同，请根据实际情况调整
    if (response && response.data) {
      items.value = response.data;
    }
  } catch (error) {
    console.error("获取积分详情失败:", error);
    // 请求失败时使用默认数据
  } finally {
    loading.value = false;
  }
}

function close() {
  emit("update:visible", false);
}
</script>

<style lang="scss" scoped>
:deep(.v-card) {
  border-radius: 40px !important;
}
:deep(.v-overlay__content) {
}
.rule-dialog-mask {
  /* v-dialog 外层遮罩样式 */
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pontos-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #2a3a1a;
  :deep(.v-table__wrapper) {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  overflow: hidden;
  margin: 0 auto;
  color: #fff;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.pontos-table th,
.pontos-table td {
  padding: 10px 16px;
  text-align: left;
}

.pontos-table tr {
  border-bottom: 1px solid #3e4e2a;
}

.pontos-table tr:last-child {
  border-bottom: none;
}

.pontos-table th {
  background: linear-gradient(180deg, #cfc804 0%, #28791c 100%) !important;
  color: #fff;
  font-weight: bold;
  font-size: 18px;
  border-bottom: none !important;
  border-radius: 0;
}

.pontos-table th:first-child {
  border-top-left-radius: 40px;
}

.pontos-table th:last-child {
  border-top-right-radius: 40px;
}

.pontos-table td {
  background: transparent;
  color: #fff;
  font-size: 16px;
}

@media (max-width: 768px) {
  .pontos-table {
    font-size: 12px;
  }

  .pontos-table th {
    font-size: 14px;
  }

  .pontos-table td {
    font-size: 14px;
  }
}
</style>
