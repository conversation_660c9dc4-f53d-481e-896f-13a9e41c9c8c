<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-16 09:29:55
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\activities\invitation\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="tsx">
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTag,
} from "element-plus";
// import { getShortUrlList } from "@/service/api/shorturl";
import { fetchInviteList } from "@/service/api/invite";
import { useTable } from "@/hooks/common/table";
import { useRouter } from "vue-router";
import { ref } from "vue";
import type {
  ShortUrlRecord,
  ShortUrlListParams,
  ShortUrlSearchForm,
} from "@/typings/shorturl";
import moment from "moment";

const router = useRouter();

// 定义搜索参数接口
interface SearchParams extends ShortUrlSearchForm {}

// 注册来源选项
const registerSourceOptions = [
  { label: "全部", value: 0 },
  { label: "渠道", value: 1 },
  { label: "代理商", value: 2 },
  { label: "平台", value: 3 },
];

// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable<any>({
  apiFn: fetchInviteList,
  apiParams: {
    page: 1,
    pagesize: 10,
    user_id: "",
    phone: "",
    source: "",
    father_id: "",
    father_id_two: "",
    father_id_three: "",
    start_time: "",
    end_time: "",
  } as ShortUrlSearchForm,
  columns: () => [
    { prop: "index", label: "序号", width: 64 },
    {
      prop: "user_id",
      label: "用户ID",
      minWidth: 120,
      showOverflowTooltip: true,
    },
    {
      prop: "nickname",
      label: "用户昵称",
      minWidth: 150,
      showOverflowTooltip: true,
    },
    {
      prop: "phone",
      label: "手机号",
      minWidth: 140,
      showOverflowTooltip: true,
    },
    {
      prop: "father_id",
      label: "一级父ID",
      minWidth: 120,
      showOverflowTooltip: true,
    },
    {
      prop: "father_id_two",
      label: "二级父ID",
      minWidth: 120,
      showOverflowTooltip: true,
    },
    {
      prop: "father_id_tree",
      label: "三级父ID",
      minWidth: 120,
      showOverflowTooltip: true,
    },
    {
      prop: "source",
      label: "注册来源",
      width: 100,
      formatter: (row: any) => {
        const source = registerSourceOptions.find(
          (item) => item.value === row.source,
        );
        return source ? source.label : row.source;
      },
    },

    {
      prop: "login_type",
      label: "登录状态",
      width: 100,
      formatter: (row: any) => {
        let text = "";
        let type: any = "info";
        switch (row.login_type) {
          case 1:
            text = "在线";
            type = "success";
            break;
          case 2:
            text = "游戏中";
            type = "warning";
            break;
          case 3:
            text = "离线";
            type = "info";
            break;
          default:
            text = "-";
            type = "info";
        }
        return <ElTag type={type}>{text}</ElTag>;
      },
    },
    {
      prop: "register_time",
      label: "注册时间",
      width: 180,
      formatter: (row: any) =>
        row.register_time
          ? moment(row.register_time).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
  ],
});

// 搜索方法
const handleSearch = () => {
  getDataByPage();
};

// 重置搜索
const handleReset = () => {
  dataRange.value = undefined;
  resetSearchParams();
};

function handleToConfig() {
  router.push("/activities/acviteconfig");
}
const dataRange = ref();
defineOptions({ name: "InvitationManage" });
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <ElCard class="search-form-card">
      <ElForm :model="searchParams" inline class="search-form">
        <ElFormItem label="用户ID">
          <ElInput
            v-model="searchParams.user_id"
            placeholder="请输入用户ID"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="手机号">
          <ElInput
            v-model="searchParams.phone"
            placeholder="请输入手机号"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="注册来源">
          <ElSelect
            v-model="searchParams.source"
            placeholder="请选择"
            clearable
          >
            <ElOption
              v-for="item in registerSourceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="一级父ID">
          <ElInput
            v-model="searchParams.father_id"
            placeholder="请输入一级父ID"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="二级父ID">
          <ElInput
            v-model="searchParams.father_id_two"
            placeholder="请输入二级父ID"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="三级父ID">
          <ElInput
            v-model="searchParams.father_id_three"
            placeholder="请输入三级父ID"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="注册时间">
          <ElDatePicker
            v-model="dataRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="x"
            @change="
                  (val: [string, string] | null) => {
                    if (val) {
                      searchParams.start_time = val[0];
                      searchParams.end_time = val[1];
                    } else {
                      searchParams.start_time = undefined;
                      searchParams.end_time = undefined;
                    }
                  }
                "
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>
    <ElCard class="sm:flex-1-hidden card-wrapper search-card" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>邀请活动列表</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :loading="loading"
            @refresh="getData"
          >
            <template #default>
              <ElButton
                type="primary"
                plain
                size="small"
                @click="handleToConfig"
              >
                <template #icon>
                  <icon-ant-design-setting-outlined />
                </template>
                邀请配置
              </ElButton>
            </template>
          </TableHeaderOperation>
        </div>
      </template>

      <div class="h-[calc(100%-70px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  }

  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 20px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select,
      .el-date-editor {
        width: 220px;
      }

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }
  }
}
.search-form-card {
  margin-bottom: 20px;

  :deep(.search-form) {
    padding: 16px;
    border-radius: 6px;

    .el-form-item {
      margin-right: 20px;
      margin-bottom: 20px;

      &:last-child {
        margin-right: 0;
      }

      .el-form-item__label {
        width: 80px !important;
        text-align: right;
        font-weight: 500;
        color: #606266;
      }

      .el-input,
      .el-select,
      .el-date-editor {
        width: 240px !important;
      }
    }
  }
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;

  th {
    background-color: #f5f7fa;
    font-weight: 600;
  }

  td {
    padding: 12px 0;
  }
}

:deep(.el-pagination) {
  margin-top: 24px;
  padding: 0;
  justify-content: flex-start;
}

:deep(.el-button) {
  // border-radius: 4px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  &:not(.is-plain) {
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
  }
}
</style>
