<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { fetchBatchUpdateVipLevel } from "@/service/api/vipLevel";

interface Props {
  operateType: UI.TableOperateType;
  rowData?: any[] | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

// VIP等级颜色映射
const vipLevelColors: Record<number, string> = {
  0: "#6b7280", // VIP1 - 灰色
  1: "#9ca3af", // VIP1 - 灰色
  2: "#3b82f6", // VIP2 - 蓝色
  3: "#22c55e", // VIP3 - 绿色
  4: "#f59e0b", // VIP4 - 橙色
  5: "#ef4444", // VIP5 - 红色
  6: "#8b5cf6", // VIP6 - 紫色
  7: "#14b8a6", // VIP7 - 青色
  8: "#f97316", // VIP8 - 深橙
  9: "#6b7280", // VIP9 - 深灰
  10: "#f59e0b", // VIP10 - 金色
};

// 表单数据
const formData = ref<VipLevelConfig[]>([]);
const loading = ref(false);
const activeTab = ref("0-5");

// 定义VIP级别数据类型
interface VipLevelConfig {
  id: number;
  level: number;
  name: string;
  max_withdrawal: number;
  withdrawal_count: number;
  monthly_charge: number;
  monthly_bet: number;
}

// 初始化表单数据
function initFormData() {
  const defaultData: VipLevelConfig[] = [];
  for (let i = 0; i <= 10; i++) {
    defaultData.push({
      id: i,
      level: i,
      name: `VIP${i}`,
      max_withdrawal: 0,
      withdrawal_count: 0,
      monthly_charge: 0,
      monthly_bet: 0,
    });
  }

  // 如果有传入的数据，则合并
  if (props.rowData && Array.isArray(props.rowData)) {
    props.rowData.forEach((item: any) => {
      const index = defaultData.findIndex((d) => d.level === item.level);
      if (index !== -1) {
        // 将后端的金额数据除以100显示
        const processedItem = {
          ...item,
          max_withdrawal: item.max_withdrawal ? (item.max_withdrawal / 100) : 0,
          monthly_charge: item.monthly_charge ? (item.monthly_charge / 100) : 0,
          monthly_bet: item.monthly_bet ? (item.monthly_bet / 100) : 0,
        };
        defaultData[index] = { ...defaultData[index], ...processedItem };
      }
    });
  }

  formData.value = defaultData;
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    initFormData();
  }
});

// 提交表单
async function handleSubmit() {
  try {
    loading.value = true;

    const submitData = {
      configs: formData.value.map((item) => ({
        id: item.id,
        level: item.level,
        name: item.name,
        max_withdrawal: Math.round(Number(item.max_withdrawal) * 100),
        withdrawal_count: Number(item.withdrawal_count),
        monthly_charge: Math.round(Number(item.monthly_charge) * 100),
        monthly_bet: Math.round(Number(item.monthly_bet) * 100),
      })),
    };
    await fetchBatchUpdateVipLevel(submitData);
    ElMessage.success("VIP等级配置更新成功");
    emit("submitted");
    visible.value = false;
  } catch (error) {
    console.error("更新VIP等级配置失败:", error);
    ElMessage.error("更新VIP等级配置失败");
  } finally {
    loading.value = false;
  }
}

// 获取VIP1-5数据
const vip0to5Data = computed(() => formData.value.slice(0, 6));

// 获取VIP6-10数据
const vip6to10Data = computed(() => formData.value.slice(6, 11));

// 限制金额输入只能输入两位小数
function handleAmountInput(item: VipLevelConfig, field: keyof VipLevelConfig, inputValue: string | number) {
  let value = String(inputValue);

  // 只允许正整数
  value = value.replace(/[^\d]/g, '');
  // 去除前导零
  value = value.replace(/^0+(?!$)/, '');

  // 更新数据
  item[field] = value as any;
}

// 限制只能输入整数
function handleIntegerInput(item: VipLevelConfig, field: keyof VipLevelConfig, inputValue: string | number) {
  let value = String(inputValue);

  // 只允许数字
  value = value.replace(/[^\d]/g, '');

  // 更新数据
  item[field] = value as any;
}
</script>

<template>
  <ElDrawer
    v-model="visible"
    title="编辑VIP等级配置"
    :size="1200"
    destroy-on-close
  >
    <div class="p-4">
      <ElTabs v-model="activeTab" type="border-card">
        <!-- VIP1-5标签页 -->
        <ElTabPane label="VIP0-5" name="0-5">
          <div class="vip-config-container">
            <!-- 表头 -->
            <div class="config-header">
              <div class="header-item level-col">等级</div>
              <div class="header-item name-col">等级名称</div>
              <div class="header-item amount-col">最大可提现金额</div>
              <div class="header-item count-col">可提现次数</div>
              <div class="header-item charge-col">近30天充值</div>
              <div class="header-item bet-col">近30天投注</div>
            </div>

            <!-- VIP0-5配置行 -->
            <div
              v-for="(item, index) in vip0to5Data"
              :key="item.level"
              class="config-row"
            >
              <div class="row-item level-col">
                <span
                  class="vip-badge"
                  :style="{ backgroundColor: vipLevelColors[item.level] }"
                >
                  VIP{{ item.level }}
                </span>
              </div>
              <div class="row-item name-col">
                <ElInput v-model="item.name" placeholder="等级名称" />
              </div>
              <div class="row-item amount-col">
                <ElInput
                  v-model="item.max_withdrawal"
                  type="text"
                  placeholder="最大提现金额"
                  @input="(value) => handleAmountInput(item, 'max_withdrawal', value)"
                />
              </div>
              <div class="row-item count-col">
                <ElInput
                  v-model="item.withdrawal_count"
                  type="text"
                  placeholder="提现次数"
                  @input="(value) => handleIntegerInput(item, 'withdrawal_count', value)"
                />
              </div>
              <div class="row-item charge-col">
                <ElInput
                  v-model="item.monthly_charge"
                  type="text"
                  :disabled="!item.level"
                  placeholder="月充值要求"
                  @input="(value) => handleAmountInput(item, 'monthly_charge', value)"
                />
              </div>
              <div class="row-item bet-col">
                <ElInput
                  v-model="item.monthly_bet"
                  type="text"
                  :disabled="!item.level"
                  placeholder="月投注要求"
                  @input="(value) => handleAmountInput(item, 'monthly_bet', value)"
                />
              </div>
            </div>
          </div>
        </ElTabPane>

        <!-- VIP6-10标签页 -->
        <ElTabPane label="VIP6-10" name="6-10">
          <div class="vip-config-container">
            <!-- 表头 -->
            <div class="config-header">
              <div class="header-item level-col">等级</div>
              <div class="header-item name-col">等级名称</div>
              <div class="header-item amount-col">最大可提现金额</div>
              <div class="header-item count-col">可提现次数</div>
              <div class="header-item charge-col">近60天充值</div>
              <div class="header-item bet-col">近60天投注</div>
            </div>

            <!-- VIP6-10配置行 -->
            <div
              v-for="(item, index) in vip6to10Data"
              :key="item.level"
              class="config-row"
            >
              <div class="row-item level-col">
                <span
                  class="vip-badge"
                  :style="{ backgroundColor: vipLevelColors[item.level] }"
                >
                  VIP{{ item.level }}
                </span>
              </div>
              <div class="row-item name-col">
                <ElInput v-model="item.name" placeholder="等级名称" />
              </div>
              <div class="row-item amount-col">
                <ElInput
                  v-model="item.max_withdrawal"
                  type="text"
                  placeholder="最大提现金额"
                  @input="(value) => handleAmountInput(item, 'max_withdrawal', value)"
                />
              </div>
              <div class="row-item count-col">
                <ElInput
                  v-model="item.withdrawal_count"
                  type="text"
                  placeholder="提现次数"
                  @input="(value) => handleIntegerInput(item, 'withdrawal_count', value)"
                />
              </div>
              <div class="row-item charge-col">
                <ElInput
                  v-model="item.monthly_charge"
                  type="text"
                  placeholder="月充值要求"
                  @input="(value) => handleAmountInput(item, 'monthly_charge', value)"
                />
              </div>
              <div class="row-item bet-col">
                <ElInput
                  v-model="item.monthly_bet"
                  type="text"
                  placeholder="月投注要求"
                  @input="(value) => handleAmountInput(item, 'monthly_bet', value)"
                />
              </div>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3 p-4">
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          保存
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
.vip-config-container {
  width: 100%;

  .config-header {
    display: flex;
    align-items: center;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    padding: 12px 8px;
    font-weight: bold;
    color: #606266;

    .header-item {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 14px;
    }
  }

  .config-row {
    display: flex;
    align-items: center;
    border: 1px solid #e4e7ed;
    border-top: none;
    padding: 8px;

    &:hover {
      background-color: #f9f9f9;
    }

    .row-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 4px;
    }
  }

  // 列宽设置
  .level-col {
    width: 80px;
    min-width: 80px;
  }

  .name-col {
    width: 120px;
    min-width: 120px;
  }

  .amount-col,
  .count-col,
  .charge-col,
  .bet-col {
    flex: 1;
    min-width: 140px;
  }
}

.vip-badge {
  display: inline-block;
  padding: 4px 8px;
  color: white;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
  text-align: center;
  min-width: 50px;
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 16px 0;
}
</style>
