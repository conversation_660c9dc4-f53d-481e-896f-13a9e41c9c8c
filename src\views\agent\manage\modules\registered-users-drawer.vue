<template>
  <ElDialog
    v-model="visible"
    title="注册人员列表"
    width="60%"
    destroy-on-close
  >
    <div class="registered-users-list-container">
      <!-- Search Area -->
      <div class="search-area">
        <ElInput placeholder="用户ID" v-model="user_id" />
        <!-- 注册时间 dropdown placeholder -->
        <ElButton type="primary" @click="handleSearch" :loading="loading">搜索</ElButton>
        <ElButton @click="handleReset" :loading="loading">重置</ElButton>
      </div>

      <!-- Table -->
      <ElTable
        :data="registeredUsersData"
        style="width: 100%"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <ElTableColumn type="index" label="序号" width="60" />
        <ElTableColumn prop="user_id" label="用户ID" />
        <ElTableColumn prop="user_name" label="用户昵称" />
        <ElTableColumn prop="register_time" label="注册时间">
          <template #default="{ row }">
            {{ formatTime(row.register_time) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="agent_id" label="邀请人ID" />
        <ElTableColumn prop="inviter_name" label="邀请人昵称" />
      </ElTable>

      <!-- Pagination -->
      <div class="pagination-area">
        <ElPagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElDialog, ElInput, ElButton, ElTable, ElTableColumn, ElPagination } from 'element-plus';
import { PropType } from 'vue';
import { fetchGetAgentRegistered_users } from '@/service/api/agent';
import moment from 'moment';

const props = defineProps({
  visible: { type: Boolean, default: false },
  agentId: { type: Number as PropType<number | null | undefined>, required: false }, // Agent ID to fetch registered users for, allow null and undefined
});

const emit = defineEmits(['update:visible']);

const visible = ref(props.visible);
const user_id = ref('');
const registeredUsersData = ref([]); // Placeholder for fetched data
const loading = ref(false);

// Pagination state
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0); // Placeholder for total count

watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal && props.agentId) {
    // Fetch data when dialog opens
    fetchRegisteredUsers(props.agentId, currentPage.value, pageSize.value);
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
});

// Placeholder function to fetch data
async function fetchRegisteredUsers(agentId: number, page: number, size: number) {
  try {
    loading.value = true;
    console.log(`Fetching registered users for agent ${agentId}, page ${page}, size ${size}`);
    const response = await fetchGetAgentRegistered_users({
      page,
      size,
      agent_id:agentId,
      user_id:user_id.value
    })
    registeredUsersData.value = response.data.data; // Clear previous data
    total.value = 0; // Reset total
  } catch (error) {
    console.error('Error fetching registered users:', error);
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  currentPage.value = 1;
  if (props.agentId) {
    fetchRegisteredUsers(props.agentId, currentPage.value, pageSize.value);
  }
}

function handleReset() {
  user_id.value = '';
  currentPage.value = 1;
  if (props.agentId) {
     fetchRegisteredUsers(props.agentId, currentPage.value, pageSize.value);
  }
}

function handleSizeChange(val: number) {
  pageSize.value = val;
  currentPage.value = 1; // Reset to first page on size change
   if (props.agentId) {
    fetchRegisteredUsers(props.agentId, currentPage.value, pageSize.value);
  }
}

function handleCurrentChange(val: number) {
  currentPage.value = val;
   if (props.agentId) {
    fetchRegisteredUsers(props.agentId, currentPage.value, pageSize.value);
  }
}

// 添加時間格式化函數
const formatTime = (time: string) => {
  if (!time) return '-';
  return moment(time).format('YYYY-MM-DD HH:mm:ss');
};

</script>

<style scoped>
.registered-users-list-container {
  padding: 20px;
}

.search-area {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end; /* Align pagination to the right as in the image */
}

/* 如果需要针对 Dialog 调整样式，可以在这里添加 */
/*
:deep(.el-dialog__body) {
  padding: 0 20px 20px 20px;
}
*/
</style>
