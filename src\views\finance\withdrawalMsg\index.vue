<script setup lang="tsx">
import { ref } from "vue";
import { ElTag } from "element-plus";
import {
  getWithdrawalList,
  getWithdrawalUserRecords,
} from "@/service/api/withdrawal";
import { useTable } from "@/hooks/common/table";
import WithdrawalMsgSearch from "./modules/withdrawal-msg-search.vue";
import type {
  WithdrawalListParams,
  WithdrawalListResponse,
  WithdrawalRecord,
} from "@/typings/withdrawal";
import type { TableColumn } from "@/typings/table";
import { useRouter } from "vue-router";

// Define table data type
interface TableItem extends WithdrawalRecord {
  index: number;
}
const router = useRouter();

// Table Hook configuration
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable<
  (params: WithdrawalListParams) => Promise<WithdrawalListResponse>,
  WithdrawalListResponse["list"][0],
  TableColumn<WithdrawalListResponse["list"][0]>
>({
  apiFn: getWithdrawalUserRecords,
  apiParams: {
    page: 1,
    size: 10,
    withdrawal_count: undefined,
    uuid: undefined,
    transaction_number: undefined,
    payment_method: undefined,
    wallet_id: undefined,
    account_num: undefined,
  },
  columns: () => [
    { prop: "index", label: "序号", minWidth: 64 },
    { prop: "uuid", label: "用户ID", minWidth: 120 },
    { prop: "wallet_id", label: "钱包ID", minWidth: 120 },
    {
      prop: "withdrawal_amount",
      label: "提现金额",
      minWidth: 120,
      formatter: (row) =>
        ` ${(row.withdrawal_amount / 100)?.toFixed(2) || "0.00"}`,
    },
    { prop: "currency", label: "币种", minWidth: 80 },
    {
      prop: "status",
      label: "状态",
      minWidth: 100,
      formatter: (row: TableItem) => (
        <ElTag
          type={
            row.status === "completed"
              ? "success"
              : row.status === "pending"
                ? "warning"
                : "danger"
          }
        >
          {row.status === "completed"
            ? "已完成"
            : row.status === "pending"
              ? "处理中"
              : "失败"}
        </ElTag>
      ),
    },
    { prop: "order_number", label: "订单号", minWidth: 140 },
    { prop: "transaction_number", label: "交易号", minWidth: 120 },
    { prop: "account_num", label: "账户号码", minWidth: 140 },
    {
      prop: "account_type",
      label: "账户类型",
      minWidth: 100,
      formatter: (row: TableItem) => {
        const types = {
          1: "CPF",
          2: "CNPJ",
          3: "PIX",
          4: "Email",
          5: "Phone",
        };
        return types[row.account_type as keyof typeof types] || "未知";
      },
    },
    {
      prop: "fee",
      label: "手续费",
      minWidth: 100,
      formatter: (row: TableItem) => (row.fee / 100).toFixed(2),
    },
    { prop: "channel_type", label: "提现方式", minWidth: 120 },
    { prop: "withdrawal_count", label: "提现次数", minWidth: 120 },
    {
      prop: "reason_failure",
      label: "失败原因",
      minWidth: 200,
      formatter: (row: TableItem) => {
        return row.reason_failure === "PAID" ? "-" : row.reason_failure;
      },
    },
    {
      prop: "created_at",
      label: "创建时间",
      minWidth: 180,
      formatter: (row: TableItem) => {
        const date = new Date(row.created_at);
        return date
          .toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
          })
          .replace(/\//g, "-");
      },
    },
  ],
});
const handleToConfig = () => {
  router.push("/finance/withdrawalmanagement");
};
defineOptions({ name: "WithdrawalRecord" });

function handleSearch() {
  console.log(searchParams);
  if (searchParams.withdrawal_count) {
    searchParams.status = "completed";
  } else {
    searchParams.status = "";
  }
  getDataByPage();
}

function handleReset() {
  resetSearchParams();
  // handleSearch();
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <WithdrawalMsgSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @refresh="getData"
        >
          <ElButton type="primary" plain size="small" @click="handleToConfig">
            <template #icon>
              <icon-ant-design-setting-outlined />
              <!-- <icon-mdi-refresh class="text-icon" style="color:#646CFF" :class="{'animate-spin': loading }" /> -->
            </template>
            提现配置
          </ElButton>
        </TableHeaderOperation>
      </template>
    </WithdrawalMsgSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
