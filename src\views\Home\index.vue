<template>
  <v-container
    fluid
    class="home-container pa-0 overflow-hidden"
    max-width="1140"
  >
    <!-- 系统公告 -->
    <div class="notice-container">
      <div class="notice-bar">
        <img src="../../assets/images/home-msg-icon.png" class="notice-icon" />
        <div class="notice-scroll">
          <div
            class="scroll-content"
            :style="{ transform: `translateX(${-scrollPosition}px)` }"
          >
            <div class="notice-items">
              <span
                v-for="(notice, index) in notices"
                :key="notice.id || index"
                class="notice-item"
                :class="{ important: notice.status === 1 }"
                @click="showNoticeDialog(notice)"
              >
                <v-icon icon="mdi-menu-right" color="#FF206E"></v-icon>
                {{ notice.subject }}
              </span>
            </div>
            <!-- 复制一份实现无缝滚动 -->
            <div class="notice-items">
              <span
                v-for="(notice, index) in notices"
                :key="`dup-${notice.id || index}`"
                class="notice-item"
                :class="{ important: notice.status === 1 }"
                @click="showNoticeDialog(notice)"
              >
                <v-icon icon="mdi-menu-right" color="#FF206E"></v-icon>
                {{ notice.subject }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="banner-wrapper mt-1 mb-1">
      <!-- 顶部横幅 -->
      <v-card
        class="mx-auto banner-carousel transparent-card"
        elevation="0"
        max-width="1140"
      >
        <Swiper
          ref="swiperRef"
          :key="banners.length"
          v-show="banners.length"
          :modules="modules"
          effect="coverflow"
          :grabCursor="true"
          :centeredSlides="true"
          :slidesPerView="1.6"
          :loopedSlides="banners.length"
          :coverflowEffect="{
            rotate: 0,
            stretch: 0,
            depth: 322,
            modifier: 3.2,
            slideShadows: false,
          }"
          :pagination="false"
          :initialSlide="initialSlide"
          :autoplay="{
            delay: 3000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }"
          :loop="true"
          class="banner-swiper"
          @swiper="onSwiper"
          @slideChange="onSlideChange"
        >
          <SwiperSlide v-for="(slide, i) in banners" :key="slide.image || i">
            <div
              class="banner-gradient-bg"
              @click="handleBannerClick(slide.hyperlink)"
            >
              <img :src="slide.image" class="game-image1" />
            </div>
          </SwiperSlide>
        </Swiper>
      </v-card>
    </div>
    <div class="notice-container mb-1">
      <div class="notice-bar notice-bar-huodong">
        <img src="../../assets/images/home-msg-icon.png" class="notice-icon" />
        <div class="notice-scroll">
          <div
            class="scroll-content"
            :style="{ transform: `translateX(${-scrollPosition}px)` }"
          >
            <div class="notice-items">
              <span
                v-for="(notice, index) in jctNotices"
                :key="notice.id || index"
                class="notice-item"
                :class="{ important: notice.status === 1 }"
                @click="showNoticeDialog(notice)"
              >
                <v-icon icon="mdi-menu-right" color="#FF206E"></v-icon>
                {{ notice.subject }}
              </span>
            </div>
            <!-- 复制一份实现无缝滚动 -->
            <div class="notice-items">
              <span
                v-for="(notice, index) in jctNotices"
                :key="`dup-${notice.id || index}`"
                class="notice-item"
                :class="{ important: notice.status === 1 }"
                @click="showNoticeDialog(notice)"
              >
                <v-icon icon="mdi-menu-right" color="#FF206E"></v-icon>
                {{ notice.subject }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 奖池 -->
    <div class="prize-pool">
      <div class="jackpot-wrapper">
        <img
          src="../../assets/images/jc-tp.png"
          width="100%"
          class="jackpot-bg"
        />
        <div class="jackpot-content">
          <JackpotBalance />
        </div>
      </div>
    </div>
    <!-- 内容区域包装器 -->
    <div class="flier-wrapper pa-2">
      <!-- 游戏分类标签 - 桌面端显示 -->
      <v-tabs
        v-model="currentTab"
        class="mt-0 game-tabs d-none d-md-block"
        align-tabs="center"
        fixed-tabs
      >
        <v-tab
          v-for="(tab, index) in tabs"
          :key="tab.name"
          :value="tab.name"
          class="custom-tab"
          :class="{ 'tab-active': currentTab === tab.name }"
        >
          <div
            class="tab-content"
            @click="router.push(tab.id ? '/game-list?id=' + tab.id : '/')"
          >
            <img :src="gameMenuItemsImages[index]" class="item-icon" />
            <span class="tab-text" v-html="tab.name"></span>
          </div>
        </v-tab>
      </v-tabs>
      <!-- 搜索框独占一行 -->
      <div class="search-row">
        <v-text-field
          v-model="searchQuery"
          placeholder="Pesquisar por nome"
          class="search-field"
          hide-details
          variant="outlined"
          density="compact"
          bg-color="rgba(0, 0, 0, 0.2)"
          @focus="isFocused = true"
          @blur="isFocused = false"
        >
          <template #prepend-inner>
            <img src="../../assets/images/query-icon.png" class="search-icon" />
          </template>
        </v-text-field>
      </div>
    </div>
    <div class="game-list-wrapper mt-1" v-if="searchResults.length">
      <div class="game-list">
        <template v-for="game in searchResults" :key="game.id">
          <v-card
            variant="text"
            class="game-card"
            @click="handleGameClick(game)"
          >
            <v-img
              :src="game.icon"
              aspect-ratio="0.84"
              cover
              class="game-image"
              lazy-src="https://via.placeholder.com/40x40?text=..."
            ></v-img>
            <div
              class="star-icon pl-1 pr-1 pb-1"
              @click.stop="handleCollection(game, game.is_favorite)"
            >
              <v-icon
                :icon="game.is_favorite ? 'mdi-star' : 'mdi-star-outline'"
                :color="game.is_favorite ? '#ff9000' : ''"
                size="22"
              ></v-icon>
            </div>
            <v-card-text class="pa-2">
              <div class="game-item-label">{{ game.game_name }}</div>
              <div class="game-item-provider">{{ game.manufacturer }}</div>
            </v-card-text>
          </v-card>
        </template>
      </div>
    </div>
    <!-- 游戏列表 -->
    <div class="game-list-wrapper mt-1">
      <template v-for="section in gameSections" :key="section.key">
        <div v-if="section?.games.length">
          <div
            class="game-title d-flex justify-space-between align-center mb-1"
          >
            <span class="d-flex justify-start align-center">
              <img
                src="@/assets/images/logo-icon.png"
                width="22"
                class="mr-1"
              />
              {{ section.title }}
            </span>
            <span
              class="title-more pa-1"
              @click="handleMoreClick(section.key)"
              v-if="
                section.key !== 'user_recent_games' &&
                section.key !== 'user_favorites'
              "
            >
              todo
              <v-icon icon="mdi-chevron-right"></v-icon>
            </span>
          </div>
          <div
            class="game-list"
            :class="{ 'history-list': section.key === 'user_recent_games' }"
          >
            <template v-for="game in section?.games" :key="game.id">
              <v-card
                variant="text"
                class="game-card"
                @click="handleGameClick(game)"
              >
                <v-img
                  :src="game.icon"
                  aspect-ratio="0.84"
                  cover
                  class="game-image"
                  lazy-src="https://via.placeholder.com/40x40?text=..."
                ></v-img>
                <div
                  class="star-icon pl-1 pr-1 pb-1"
                  @click.stop="handleCollection(game, game.is_favorite)"
                >
                  <v-icon
                    :icon="game.is_favorite ? 'mdi-star' : 'mdi-star-outline'"
                    :color="game.is_favorite ? '#ff9000' : ''"
                    size="22"
                  ></v-icon>
                </div>
                <v-card-text class="pa-2">
                  <div class="game-item-label">{{ game.game_name }}</div>
                  <div class="game-item-provider">{{ game.manufacturer }}</div>
                </v-card-text>
              </v-card>
            </template>
          </div>
        </div>
      </template>
    </div>
    <PageBottom />
    <NoticeDialog
      v-if="selectedNotice"
      :notice="selectedNotice"
      @close="selectedNotice = null"
    />
    <CommonDialog
      :show="commonDialogShow"
      @update:show="commonDialogShow = $event"
      :dialogObj="dialogObj"
    />
    <PddActivityModal
      ref="showPddModal"
      @close="handlePddModalClose"
      @invite="handlePddModalJoin"
    />
    <ActivityInviteModal
      v-model:show="showInviteModal"
      :invite-code="inviteCode"
      @join="handleInviteModalJoin"
    />
    <PinduoduoDialog ref="pinduoduoDialogRef" />
    <CoinEffect :show="showCoinEffect" @finish="handleFinish" />
    <!-- 中奖弹窗 -->
    <RewardDialog
      v-model:show="rewardDialog"
      :amount="rewardAmount"
      @close="closeRewardDialog"
    />
  </v-container>

  <!-- 金币爆炸特效 - 固定在页面中心 -->
  <div v-if="gifShow" class="coin-explosion-overlay">
    <img
      ref="gifImageRef"
      v-if="gifShow"
      src="@/assets/images/coin.gif"
      class="coin-explosion-gif"
      @load="onGifLoad"
      @error="onGifError"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  computed,
  getCurrentInstance,
  nextTick,
  shallowRef,
} from "vue";
import gameIcon1 from "@/assets/images/game-icon-01.png";
import gameIcon2 from "@/assets/images/game-icon-02.png";
import gameIcon8 from "@/assets/images/game-icon-08.png";
import gameIcon9 from "@/assets/images/game-icon-09.png";
import gameIcon5 from "@/assets/images/game-icon-05.png";
import gameIcon7 from "@/assets/images/game-icon-07.png";
import gameIcon3 from "@/assets/images/game-icon-03.png";
import gameIcon4 from "@/assets/images/game-icon-04.png";
import menuIcon6 from "@/assets/images/purse-icon.png";
import { getNoticeList, getShortLinkDetail } from "@/api/auth";
import { getCarouselList } from "@/api/home";
import { checkActivityPopup, homePopup } from "@/api/activity";
import {
  getGameFavorites,
  addGameCollection,
  removeGameCollection,
  searchGetGameList,
} from "@/api/game";
import NoticeDialog from "@/components/NoticeDialog.vue";
import CommonDialog from "@/components/CommonDialog.vue";
import router from "@/router";
import { loginEvent, SHOW_LOGIN_EVENT } from "@/api/request.ts";
import { getToken } from "@/utils/auth.ts";
import { useStore } from "vuex";
import JackpotBalance from "@/components/JackpotBalance.vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { EffectCoverflow, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import PddActivityModal from "@/components/PddActivityModal.vue";
import ActivityInviteModal from "@/components/ActivityInviteModal.vue";
import PinduoduoDialog from "@/components/PinduoduoDialog.vue";
import { enterPddActivity } from "@/api/home";
import { supportActivity } from "@/api/activity";
import { parseUrlParams } from "@/utils/url";
import CoinEffect from "@/components/CoinEffect.vue";
import type { GameItem } from "@/types/game";
import type { Notice } from "@/types/notice";
import PageBottom from "@/components/PageBottom.vue";
import RewardDialog from "@/components/RewardDialog.vue";

// 类型定义
interface BannerItem {
  image: string;
  hyperlink?: string;
}

interface TabItem {
  name: string;
  id?: number;
}

interface ActivityData {
  invite_code?: string;
  activity_id?: string;
}

const store = useStore();
const { proxy } = getCurrentInstance()!;

// 获取奖池金额
const balanceChange = computed(() => {
  return store.getters["jackpot/balanceChange"];
});

const balanceChangeTime = computed(() => {
  return store.getters["jackpot/balanceChangeTime"];
});

// 監聽獎池餘額變化
watch(
  balanceChange,
  (newBalance, oldBalance) => {
    if (newBalance !== oldBalance) {
      const newNotice: Notice = {
        id: new Date().getTime(),
        subject: "foram depositados no prêmio!",
        content: "R$" + (newBalance || 0) / 100,
        created_at:
          proxy?.$filters?.date(balanceChangeTime.value || new Date()) ||
          new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 1,
        notice_type: 3,
      };
      jctNotices.value?.push(newNotice);
    }
  },
  { immediate: false }
);

// 首页活动弹框
const commonDialogShow = ref(false);
const dialogObj = ref<any>();

const getHomePopup = () => {
  homePopup().then((res: any) => {
    console.log(res);
    if (res.length) {
      commonDialogShow.value = true;
      dialogObj.value = res[0];
    }
  });
};

// 金币爆炸特效
const gifShow = ref(false);
const gifImageRef = ref<HTMLImageElement | null>(null);
// 显示奖励弹窗
const rewardDialog = ref(false);
const rewardAmount = ref();
// GIF加载完成处理
const onGifLoad = () => {
  console.log("GIF loaded");
  gifShow.value = false;
  if (rewardAmount.value) {
    rewardDialog.value = true;
  } else {
    showPddModal.value?.showDialog();
  }
};

// GIF加载错误处理
const onGifError = () => {
  console.error("Failed to load GIF");
  gifShow.value = false;
  if (rewardAmount.value) {
    rewardDialog.value = true;
  } else {
    showPddModal.value?.showDialog();
  }
};
const closeRewardDialog = () => {
  rewardDialog.value = false;
  rewardAmount.value = "";
  showPddModal.value?.showDialog();
};
// 游戏图片数组
const gameMenuItemsImages = [
  gameIcon9,
  gameIcon8,
  gameIcon2,
  gameIcon1,
  gameIcon5,
  gameIcon7,
  gameIcon3,
  gameIcon4,
];

// 搜索查询
const searchQuery = ref("");

// 搜索框焦点状态
const isFocused = ref(false);

// 当前激活的标签
const currentTab = ref("Todos");

// 游戏分类标签
const tabs = ref<TabItem[]>([
  { name: "Salão" },
  { name: "Bônus Slots", id: 3 },
  { name: "Hot", id: 1 },
  { name: "slots", id: 2 },
  { name: "Cassino ao Vivo", id: 4 },
  { name: "Pôquer", id: 7 },
  { name: "Pescaria", id: 8 },
  { name: "Coluna", id: 9 },
]);

// 轮播图数据
const banners = ref<BannerItem[]>([]);

// Swiper实例引用
const swiperRef = ref();

// Swiper初始居中索引
const initialSlide = computed(() => {
  return banners.value.length > 0 ? Math.floor(banners.value.length / 2) : 0;
});

// Swiper初始化完成回调
const onSwiper = (swiper: any) => {
  if (swiper && swiper.autoplay) {
    swiper.autoplay.start();
  }
};

// Swiper滑动变化回调
const onSlideChange = (swiper: any) => {
  // 可以在这里添加滑动变化的逻辑
};

// 使用shallowRef优化大数组的性能
const notices = shallowRef<Notice[]>([]);
const jctNotices = shallowRef<Notice[]>([]);
// 移除 gameHot1、gameHot2、gameHot3
const searchResults = shallowRef<GameItem[]>([]);

// 滚动位置
const scrollPosition = ref(0);
let scrollInterval: NodeJS.Timeout | null = null;

// 优化计算属性，缓存DOM查询结果
const noticeElement = computed(() => {
  return document.querySelector(".notice-items");
});

// 优化滚动逻辑
const startScroll = () => {
  scrollInterval = setInterval(() => {
    scrollPosition.value += 1;
    const element = noticeElement.value;
    if (element && scrollPosition.value >= element.scrollWidth / 2) {
      scrollPosition.value = 0;
    }
  }, 50);
};

// 停止滚动
const stopScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 热门游戏数据
const loading = ref(false);
const gameSections = ref([]);
// 动态加载游戏数据并组装各分类
const loadGamesWithRetry = async (retryCount = 3) => {
  for (let i = 0; i < retryCount; i++) {
    try {
      const response = await getGameFavorites();
      // 分类映射
      const sectionMap = [
        { key: "user_favorites", title: "Favorito" },
        { key: "user_recent_games", title: "História" },
        { key: "bonus_slots_games", title: "Bônus Slots" },
        { key: "system_hot_games", title: "Hot" },
        { key: "slots_games", title: "Slots" },
        { key: "live_casino_games", title: "Cassino ao Vivo" },
        { key: "cartoes_games", title: "Pôquer" },
        { key: "pescaria_games", title: "Pescaria" },
        { key: "coluna_games", title: "Coluna" },
      ];
      gameSections.value = sectionMap
        .map((item) => ({
          ...item,
          games: response[item.key] || [],
        }))
        .filter((item) => item.games && item.games.length > 0);
      break;
    } catch (error) {
      console.error(`Failed to load games (attempt ${i + 1}):`, error);
      if (i === retryCount - 1) {
        console.error("All retry attempts failed");
      }
    }
  }
};

// 选中的公告
const selectedNotice = ref<Notice | null>(null);

// 显示公告弹窗
const showNoticeDialog = (notice: Notice) => {
  selectedNotice.value = notice;
};

// 处理轮播图点击
const handleBannerClick = (hyperlink?: string) => {
  if (hyperlink) {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      setTimeout(() => {
        window.location.href = hyperlink;
      }, 100);
    } else {
      window.open(hyperlink, "_self", "_self");
    }
  }
};

// 处理收藏游戏
const isCollectedLoading = ref(false);
// 优化收藏操作，添加乐观更新
const handleCollection = async (
  game: GameItem,
  isCollected: boolean | undefined
) => {
  if (!getToken()) {
    loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    return;
  }

  // 乐观更新
  const originalState = game.is_favorite;
  game.is_favorite = !isCollected;

  isCollectedLoading.value = true;
  try {
    if (isCollected) {
      await removeGameCollection({
        game_id: game.id.toString(),
      });
    } else {
      await addGameCollection({
        game_uid: game.game_uid,
        user_id: store.state.auth.user.id,
        game_id: game.id,
      });
    }
    console.log("收藏成功");
    // 只在成功时重新加载游戏列表
    await loadGamesWithRetry();
  } catch (error) {
    console.error("Failed to handle collection:", error);
    // 恢复原始状态
    game.is_favorite = originalState;
  } finally {
    isCollectedLoading.value = false;
  }
};

// 处理游戏点击
const handleGameClick = (game: GameItem) => {

  router.push(`/game/${game.game_uid}?uuid=` + game.id);
};

// 处理更多按钮点击
const handleMoreClick = (key: string) => {
  const map: Record<string, string> = {
    system_hot_games: "/game-list?id=1",
    bonus_slots_games: "/game-list?id=3",
    slots_games: "/game-list?id=2",
    live_casino_games: "/game-list?id=4",
    cartoes_games: "/game-list?id=7",
    pescaria_games: "/game-list?id=8",
    coluna_games: "/game-list?id=9",
    // 其他映射
  };
  if (map[key]) {
    router.push(map[key]);
  }
};

// 搜索结果
const showSearchResults = ref(false);
const searchLoading = ref(false);
let searchTimeout: NodeJS.Timeout | null = null;

// 优化搜索防抖，使用更高效的实现
const debouncedSearch = (() => {
  let timeoutId: NodeJS.Timeout | null = null;

  return async (query: string) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(async () => {
      if (query.trim()) {
        searchLoading.value = true;
        try {
          const response = await searchGetGameList({
            search_key: query,
            page_no: 1,
            page_size: 8,
          });
          if (response?.data) {
            searchResults.value = response?.data;
            showSearchResults.value = true;
          }
        } catch (error) {
          console.error("Failed to search games:", error);
        } finally {
          searchLoading.value = false;
        }
      } else {
        searchResults.value = [];
        showSearchResults.value = false;
      }
    }, 300);
  };
})();

// 优化监听搜索输入
watch(searchQuery, (newValue) => {
  debouncedSearch(newValue);
});

const modules = [EffectCoverflow, Pagination, Autoplay];

// 显示拼多多活动弹窗
const showPddModal = ref(); // 引导弹窗
const pinduoduoDialogRef = ref();

// 显示活动助力弹窗
const showInviteModal = ref(false);
const inviteCode = ref("");

// 检查拼多多活动
const checkPddActivity = async () => {
  try {
    const response: any = await enterPddActivity();
    console.log(response);
    if (!response.id) {
      showPddModal.value?.showDialog();
    } else {
      sessionStorage.setItem("pddActivityData", JSON.stringify(response));
    }
  } catch (error) {
    console.error("Failed to check PDD activity:", error);
  }
};

// 处理拼多多活动引导弹窗关闭
const handlePddModalClose = () => {
  showPddModal.value?.handleClose();
};

// 处理拼多多活动引导弹窗关闭，打开拼多多活动弹窗
const handlePddModalJoin = () => {
  pinduoduoDialogRef.value?.show();
};

// 处理活动助力弹窗加入
const handleInviteModalJoin = async () => {
  try {
    const res = await supportActivity({
      invite_code: inviteCode.value,
      is_new: localStorage.getItem("isNew"),
    });
    if (res && res?.amount) {
      rewardAmount.value = res.amount;
    }
    showInviteModal.value = false;
    gifShow.value = true;
  } catch (error) {
    showInviteModal.value = false;
    console.error("Failed to support activity:", error);
  }
  localStorage.removeItem("inviteCode");
  localStorage.setItem("isNew", "false");
};

const activityObj = ref<ActivityData>(); // 解析后的活动数据

// 监听 Vuex 中的 token 变化
watch(
  () => store.state.auth.token,
  async (token) => {
    if (token) {
      // 登录成功后，重新加载游戏列表
      loadGamesWithRetry();
    }
    if (
      token &&
      !sessionStorage.getItem("pddActivityData") &&
      !activityObj.value?.invite_code
    ) {
      checkPddActivity();
    }
    if (!token) {
      showInviteModal.value = false;
      handlePddModalClose();
    }
    // 处理助力弹窗
    if (
      token &&
      activityObj.value?.pdd_invite_code
    ) {
      // 检测用户是否参加过该邀请活动
      const data = await checkActivityPopup(activityObj.value.pdd_invite_code);
      if (data && data?.code !== 200) {
        localStorage.removeItem("inviteCode");
        return;
      }
      inviteCode.value = activityObj.value.pdd_invite_code;
      showInviteModal.value = true;
    }
  },
  { immediate: true }
);

const showCoinEffect = ref(false);
function handleFinish() {
  showCoinEffect.value = false;
  // 如果已登录则检查活动
  if (
    getToken() &&
    !sessionStorage.getItem("pddActivityData") &&
    !localStorage.getItem("inviteCode")
  ) {
    checkPddActivity();
  }
}

// 优化公告加载
const loadNoticesWithRetry = async (retryCount = 3) => {
  for (let i = 0; i < retryCount; i++) {
    try {
      loading.value = true;
      const response: any = await getNoticeList({
        notice_type: 0, // 获取所有类型的公告
        sort: "-id",
        page: 1,
        size: 10,
      });

      if (response?.data) {
        notices.value =
          response.data.filter((e: Notice) => e.notice_type == 2) || [];
        jctNotices.value = response.data.filter(
          (e: Notice) => e.notice_type == 3
        ).length
          ? response.data.filter((e: Notice) => e.notice_type == 3)
          : [];
      }
      break;
    } catch (error) {
      console.error(`Failed to load notices (attempt ${i + 1}):`, error);
      if (i === retryCount - 1) {
        console.error("All retry attempts failed");
      }
    } finally {
      loading.value = false;
    }
  }
};

// 优化轮播图加载
const loadBannersWithRetry = async (retryCount = 3) => {
  for (let i = 0; i < retryCount; i++) {
    try {
      const response = await getCarouselList({
        position: 1,
        sort: "-created_at",
        page: 1,
        size: 5,
      });
      if (response) {
        banners.value = response.map((item) => ({
          image: item.image_url,
          hyperlink: item.hyperlink,
        }));

        // 数据加载完成后，确保Swiper自动播放
        nextTick(() => {
          if (swiperRef.value && swiperRef.value.swiper) {
            setTimeout(() => {
              swiperRef.value.swiper.autoplay.start();
            }, 200);
          }
        });
      }
      break;
    } catch (error) {
      console.error(`Failed to load banners (attempt ${i + 1}):`, error);
      if (i === retryCount - 1) {
        console.error("All retry attempts failed");
      }
    }
  }
};

// 生命周期钩子
onMounted(async () => {
  // 并行加载数据以提高性能
  await Promise.allSettled([
    loadBannersWithRetry(),
    loadNoticesWithRetry(),
    loadGamesWithRetry(),
    getHomePopup(),
  ]);

  getHomePopup();
  if (!sessionStorage.getItem("hasShownCoinEffect")) {
    showCoinEffect.value = true;
    sessionStorage.setItem("hasShownCoinEffect", "1");
  } else if (
    getToken() &&
    !sessionStorage.getItem("pddActivityData") &&
    !localStorage.getItem("inviteCode")
  ) {
    checkPddActivity();
  }

  // 确保Swiper自动播放启动
  setTimeout(() => {
    if (swiperRef.value && swiperRef.value.swiper) {
      swiperRef.value.swiper.autoplay.start();
    }
  }, 1000);

  startScroll();
  if (localStorage.getItem("inviteCode")) {
    getShortLinkDetail(localStorage.getItem("inviteCode") || "")
      .then(async (res: any) => {
        const origin_link = res?.origin_link || "";
        if (origin_link) {
          activityObj.value = parseUrlParams(origin_link);
          if (
            activityObj.value?.pdd_invite_code &&
            getToken()
          ) {
            // 检测用户是否参加过该邀请活动
            const data = await checkActivityPopup(
              activityObj.value.pdd_invite_code
            );
            console.log(data);
            if (data && data?.code !== 200) {
              localStorage.removeItem("inviteCode");
              return;
            }
            inviteCode.value = activityObj.value.pdd_invite_code;
            showInviteModal.value = true;
          }
        }
      })
      .catch((err) => {});
  }
});

onUnmounted(() => {
  stopScroll();
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/home/<USER>";

.game-item-label {
  font-size: 14px;
  color: #fff;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.game-item-provider {
  font-size: 12px;
  color: #ffdf00;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.star-icon {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 1;
}

.game-card {
  cursor: pointer;
  transition: transform 0.2s;
  width: auto !important;

  &:hover {
    transform: scale(1.05);
  }
}

.title-more {
  cursor: pointer;
  color: #005223 !important;
  font-size: 16px !important;
  // height: 30px !important;
  font-weight: bold;
  background: linear-gradient(180deg, #ffea30 0%, #6db60e 100%) !important;
  border-radius: 4px 4px 4px 4px !important;
  padding: 1px 4px !important;

  &:hover {
    color: #fff;
  }
}

.search-results-wrapper {
  padding: 16px;
  margin: 0 12px;
  border-radius: 8px;
}

.search-results-title {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}

.search-results-list {
  overflow-x: auto;
  padding-bottom: 8px;
  gap: 12px;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
}

.jackpot-wrapper {
  position: relative;
  width: 100%;
  margin-top: -50px;

  .jackpot-bg {
    display: block;
    width: 100%;
  }

  .jackpot-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    z-index: 1;
  }
}

.banner-wrapper {
  align-items: center;
  justify-content: center;
  height: 195px;
  width: 1140px;
  background: transparent;
  margin: 32px auto 0 auto;
}

.banner-swiper {
  width: 1140px;
  box-sizing: border-box;
  overflow: visible;
  background: transparent;
}

.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 195px;
  transition: transform 0.3s;
  z-index: 2;
}

.banner-gradient-bg {
  width: 695px;
  border-radius: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none !important;
  box-shadow: none !important;
}

.game-image1 {
  width: 695px;
  height: 195px;
  border-radius: 0;
  display: block;
}

.transparent-card {
  background: transparent !important;
  box-shadow: none !important;
}

.prize-pool {
  margin-top: 50px;
}

/* 金币爆炸特效样式 */
.coin-explosion-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: none;
}

.coin-explosion-gif {
  max-width: 500px;
  max-height: 500px;
  object-fit: contain;
}

.activity-invite-modal {
  position: relative;
  background: linear-gradient(180deg, #fdf6c7 0%, #facc2e 100%);
  border-radius: 20px !important;
  overflow: hidden;

  .v-card-title,
  .v-card-text {
    color: #ff0000 !important;
    text-align: center;
  }
}

.join-btn {
  height: 56px;
  min-width: 180px;
  width: 80%;
  margin: 16px auto 8px auto;
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: none;
  border-radius: 22px;
  background: url("@/assets/images/h5/activity-button.png") no-repeat !important;
  background-size: 100% 100% !important;
  background-position: center !important;
  color: white !important;
  box-shadow: none;
  justify-content: center;
  align-items: center;
}

.history-list {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 6px;
}

.flier-wrapper {
  // display: flex;
  // align-items: center;
  // justify-content: flex-end;
}

.search-row {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.search-field {
  max-width: 300px;
  width: 100%;
}
</style>
