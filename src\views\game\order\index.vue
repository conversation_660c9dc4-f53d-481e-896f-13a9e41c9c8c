<script setup lang="tsx">
import { <PERSON><PERSON><PERSON><PERSON>, ElTag } from 'element-plus';
import { getTransactionList } from '@/service/api/gameprodect';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { formatDate } from '@/utils/format';

// 定义搜索参数接口
interface SearchParams {
  current?: number;
  size?: number;
  user_id?: number;
  game_id?: string;
  start_time?: string;
  end_time?: string;
}

// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: getTransactionList,
  apiParams: {
    current: 1,
    size: 20,
    user_id: undefined,
    game_id: undefined,
    start_time: undefined,
    end_time: undefined
  } as SearchParams,
  columns: () => [
    { prop: 'index', label: '序号', width: 64 },
    { prop: 'serial_number', label: '交易编号', minWidth: 180, showOverflowTooltip: true },
    { prop: 'game_round', label: '游戏标识', minWidth: 220, showOverflowTooltip: true },
    { prop: 'user_id', label: '用户ID', minWidth: 120, showOverflowTooltip: true },
    { prop: 'nick_name', label: '昵称', minWidth: 120, showOverflowTooltip: true },
    { prop: 'phone', label: '手机号', minWidth: 120, showOverflowTooltip: true },
    { prop: 'game_category', label: '游戏类型', minWidth: 120, showOverflowTooltip: true },
    {
      prop: 'bet_amount',
      label: '下注金额',
      width: 100,
      formatter: row => (row.bet_amount / 100).toFixed(2)
    },
    {
      prop: 'win_amount',
      label: '赢取金额',
      width: 100,
      formatter: row => (row.win_amount / 100).toFixed(2)
    },
    { prop: 'currency_code', label: '货币', width: 100 },
    {
      prop: 'trans_time',
      label: '交易时间',
      width: 180,
      formatter: row => formatDate(row.trans_time)
    }
  ]
});

// 处理时间范围变化
const handleTimeRangeChange = (val: [string, string] | null) => {

  console.log(val);

  if (val) {
    searchParams.start_time = val[0];
    searchParams.end_time = val[1];
  } else {
    searchParams.start_time = undefined;
    searchParams.end_time = undefined;
  }
};

defineOptions({ name: 'OrderManage' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto">
    <div class="search-wrapper">
      <ElForm :model="searchParams" inline>
        <ElRow :gutter="16">
          <ElCol :span="6">
            <ElFormItem label="用户ID">
              <ElInput v-model="searchParams.user_id" placeholder="请输入用户ID" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="游戏ID">
              <ElInput v-model="searchParams.game_id" placeholder="请输入游戏ID" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="10">
            <ElFormItem label="时间">
              <ElDatePicker v-model="searchParams.time_range" type="datetimerange" range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="X"
                @change="handleTimeRangeChange" />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="16">
          <ElCol :span="8">
            <ElFormItem>
              <ElButton type="primary" @click="getDataByPage()">搜索</ElButton>
              <ElButton @click="resetSearchParams()">重置</ElButton>
            </ElFormItem>
          </ElCol>
          <ElCol :span="16">
              <div class="header-operation">
                <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @refresh="getData">
                  <span style="height: 35px;width: 1px;background: #e5e6eb;"></span>
                </TableHeaderOperation>
              </div>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable v-loading="loading" height="100%" class="sm:h-full" :data="data" row-key="id">
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-start">
          <ElPagination v-if="mobilePagination.total" layout="total,prev,pager,next,sizes" v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']" @size-change="mobilePagination['size-change']" />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select,
      .el-date-editor {
        width: 200px;
      }
    }
  }
}
</style>
