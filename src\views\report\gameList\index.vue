<script setup lang="tsx">
import { ref, reactive, onMounted } from 'vue';
import { ElImage } from 'element-plus';
import { fetchGameStatisticsList } from '@/service/api';
import GameListSearch from './modules/game-list-search.vue';

const searchParams = reactive({
  manufacturer_id: '',
  sort_by: 'profit',
  game_name: ''
});

const loading = ref(false);
const data = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

function getData() {
  loading.value = true;
  fetchGameStatisticsList({
    ...searchParams,
    page: page.value,
    size: pageSize.value
  }).then(res => {
    data.value = res?.data?.data || [];
    total.value = res?.data?.count || 0;
  }).finally(() => {
    loading.value = false;
  });
}

function handleSearch() {
  page.value = 1;
  getData();
}

function handleReset() {
  searchParams.manufacturer = '';
  searchParams.sortType = 'user_count';
  searchParams.gameName = '';
  handleSearch();
}

onMounted(getData);
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto">
    <GameListSearch v-model:model="searchParams" @reset="handleReset" @search="handleSearch">
      <template #table-operation>
        <TableHeaderOperation :loading="loading" @refresh="getData" :isNoDelete="true" >
          <span style="width: 1px;height: 35px;background: #e5e6eb;"></span>
        </TableHeaderOperation>
      </template>
    </GameListSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable v-loading="loading" height="100%" class="sm:h-full main-table" :data="data" row-key="id">
          <ElTableColumn type="index" label="序号" width="60"/>
          <ElTableColumn prop="game_icon" label="游戏图标" min-width="120">
            <template #default="{ row }">
              <ElImage
                :src=row.game_icon
                preview-teleported="body"
                :preview-src-list="[row.game_icon]"
                fit="cover"
                class="h-40px"
              />
            </template>
          </ElTableColumn>
          <ElTableColumn prop="game_name" label="游戏名称" min-width="120">
          </ElTableColumn>
          <ElTableColumn prop="manufacturer_name" label="所属厂商" min-width="100">
            <template #default="{ row }">
              <ElTag :type="row.manufacturer_tag_type">{{ row.manufacturer_name }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="launch_time" label="上线时间" min-width="120" />
          <ElTableColumn prop="user_count" label="累计用户数" min-width="120" />
          <ElTableColumn prop="bet_amount" label="累计打码量" min-width="120">
            <template #default="{ row }">
              {{ row.total_code_amount }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="profit" label="累计盈利" min-width="120">
            <template #default="{ row }">
              {{ row.total_profit }}
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="total > 0"
          background
          layout="total,prev,pager,next,sizes"
          :total="total"
          :current-page="page"
          :page-size="pageSize"
          @update:current-page="val => { page = val; getData(); }"
          @update:page-size="val => { pageSize = val; page = 1; getData(); }"
        />
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>