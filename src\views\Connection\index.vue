<template>
  <v-container class="check-in-container" max-width="940">
    <CommonDialog
      :show="showTipDialog"
      @update:show="showTipDialog = $event"
      :dialogObj="dialogObj"
    />
    <!-- 顶部签到卡片 -->
    <div class="check-in-card">
      <div class="card-header">
        <img src="@/assets/images/h5/Sign.png" />
      </div>

      <!-- VIP等级选择 -->
      <div class="vip-section d-flex flex-column justify-center">
        <div>
          <div class="section-title">BONUS DE Login VIP</div>
          <!-- 渐变线 -->
          <div class="line mb-4"></div>
          <div class="vip-grid">
            <v-btn
              v-for="i in 10"
              :key="i"
              :class="['vip-btn', { active: currentVip === i }]"
              @click="currentVip = i"
            >
              Entrada<br />VIP{{ i }}
            </v-btn>
          </div>
        </div>
        <!-- 签到奖励展示 -->
        <div class="rewards-section">
          <div class="rewards-grid">
            <div
              v-for="rule in currentVipRules"
              :key="rule.day"
              class="reward-item"
              :class="{ 'box-reward': rule.day === 7 }"
            >
              <div class="day-label">Dia {{ rule.day }}</div>
              <div class="line" style="height: 1px"></div>
              <div
                class="reward-content d-flex flex-column align-center justify-center pa-2"
              >
                <img
                  v-if="rule.day !== 7"
                  src="@/assets/images/check-in-icon.png"
                  class="reward-icon"
                />
                <img
                  v-else
                  src="@/assets/images/check-in-day-7.png"
                  class="box-icon"
                />
                <div class="reward-amount">R${{ rule.cost / 100 }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 签到按钮 -->
        <v-btn
          class="check-in-btn"
          :disabled="isCheckedToday"
          @click="handleCheckIn"
        >
          {{ isCheckedToday ? "Já registrado" : "Check-in" }}
        </v-btn>

        <!-- VIP等级提示 -->
        <div class="vip-level-tip">seu nível: vip{{ userVipLevel }}</div>
      </div>

      <!-- 规则说明 -->
      <div class="rules-section">
        <div class="section-title">Regras de check-in</div>
        <div class="rules-content">
          <p style="color: #ff5989">
            Quanto maior o nivel VIP,mais ricos os premios
          </p>
          <p>{{ checkinData?.rules_text }}</p>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { showSuccess, showError, showWarning } from "@/utils/toast";
import { doCheckIn } from "@/api/auth";
import { useStore } from "vuex";
import { getCheckinInfo } from "@/api/checkin";
import CommonDialog from "@/components/CommonDialog.vue";

const store = useStore();
const currentVip = ref(1);
const isCheckedToday = ref(false);
const checkinData = ref<any>(null);
const showTipDialog = ref(false);

const dialogObj = ref({
  title: "Dica",
  content: "Os membros podem participar da atividade de check-in.",
  confirm_text: "Torne-se um VIP",
  hyperlink: window.location.origin + "/records",
});

// 获取当前VIP等级的奖励规则
const currentVipRules = computed(() => {
  if (!checkinData.value?.reward_rules) return [];
  const rule = checkinData.value.reward_rules.find(
    (r: any) => r.name === `vip${currentVip.value}`
  );
  return rule?.data || [];
});

// 获取用户 VIP 等级
const userVipLevel = computed(() => {
  console.log(checkinData.value);
  return checkinData.value?.vip_level || 0;
});

// 加载签到相关信息
const loadCheckinInfo = async () => {
  try {
    const response = await getCheckinInfo(store.state.auth.user.id);
    if (response) {
      checkinData.value = response;
      currentVip.value = response.vip_level || 1;
      // 检查今天是否已签到
      if (response.last_checkin) {
        const lastCheckinDate = new Date(response.last_checkin);
        const today = new Date();
        isCheckedToday.value =
          lastCheckinDate.toDateString() === today.toDateString();
      }
    }
  } catch (error) {
    console.error("Failed to load checkin info:", error);
    showError("Falha ao carregar informações de check-in");
  }
};

// 处理签到
const handleCheckIn = async () => {
  try {
    const response = await doCheckIn({
      user_id: store.state.auth.user?.id || null,
    });
    if (!response.code) {
      isCheckedToday.value = true;
      showSuccess("Check-in bem-sucedido!");
      // 重新加载签到信息
      await loadCheckinInfo();
    } else {
      showTipDialog.value = true;
    }
  } catch (error) {
    console.log(error);
    showWarning(error);
  }
};

// 组件挂载时加载签到信息
onMounted(() => {
  loadCheckinInfo();
});
</script>

<style lang="scss" scoped>
.check-in-container {
  min-height: 100vh;
}

.card-header {
  text-align: center;
  position: relative;
  margin-bottom: 12px;
  img {
    width: 100%;
    height: auto;
  }
}

.vip-section {
  margin-bottom: 24px;
  background: #343f6b;
  border-radius: 16px;
  padding: 20px 20px 0;
  .line {
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, transparent, #36a766, transparent);
  }

  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 16px;
    text-align: center;
  }

  .vip-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 16px;
  }

  .vip-btn {
    height: 48px;
    background: #23283d !important;
    color: #fff !important;
    border-radius: 8px !important;
    font-size: 14px;
    text-transform: none;
    line-height: 1.2;

    &.active {
      background: linear-gradient(0deg, #c9b737, #2abb27) !important;
    }
  }
}

.rewards-section {
  margin-bottom: 24px;

  .rewards-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-bottom: 24px;
  }

  .reward-item {
    background: #2b324d;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    position: relative;
    border: 1px solid #84f582;

    &.box-reward {
      grid-column: span 2;
    }

    .day-label {
      font-size: 14px;
      color: #fff;
      margin-bottom: 8px;
    }

    .reward-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }

    .reward-icon {
      width: 76px;
    }
    .box-icon {
      height: 53px;
    }
    .reward-amount {
      font-size: 17px;
      color: #fff;
      font-weight: 500;
    }
  }
}

.check-in-btn {
  height: 48px;
  width: 60%;
  margin: 0 auto;
  background: linear-gradient(0deg, #c9b737, #2abb27) !important;
  color: #fff !important;
  font-size: 17px;
  font-weight: 500;
  text-transform: none;
  border-radius: 24px !important;
  margin-bottom: 8px;

  &:disabled {
    background: #a5a5ba !important;
    cursor: not-allowed;
  }
}

.vip-level-tip {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin-bottom: 8px;
}

.rules-section {
  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    padding: 16px;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    border-radius: 8px 8px 0 0;
    text-align: center;
  }

  .rules-content {
    overflow: hidden;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    padding: 16px 20px;
    background: #343f6b;
    border-radius: 0 0 14px 14px;
    margin-bottom: 30px;

    p {
      margin-bottom: 12px;
    }
  }
}

@media (max-width: 768px) {
  .check-in-container {
    padding: 10px 16px 16px 16px;
  }

  .check-in-card {
    // padding: 16px;
  }

  .card-header {
    // padding: 24px 16px;
    margin-bottom: 0px;
    .title {
      font-size: 20px;
    }

    .subtitle {
      font-size: 14px;
    }

    .check-in-icon {
      width: 48px;
      height: 48px;
    }
  }

  .vip-section {
    .vip-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .vip-btn {
      height: 40px;
      font-size: 12px;
    }
  }

  .rewards-section {
    .rewards-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .reward-item {
      padding: 12px;

      &.box-reward {
        grid-column: span 2;

        .reward-content {
          gap: 12px;
        }

        .reward-icon {
          width: 48px;
          height: 48px;
        }

        .reward-amount {
          font-size: 17px;
        }
      }

      .day-label {
        font-size: 12px;
      }
      .reward-icon {
        width: 32px;
        &.box-icon {
          width: 40px;
        }
      }
      .reward-amount {
        font-size: 14px;
      }
    }
  }
}
</style>
