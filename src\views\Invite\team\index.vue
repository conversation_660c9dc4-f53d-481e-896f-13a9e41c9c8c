<template>
  <v-container class="invite-container" max-width="940">
    <!-- 團隊成員總數 -->
    <div class="team-members-total">
      Me<PERSON>ros da equipe {{ totalTeamMembers }} pessoas
    </div>

    <!-- 分級明細 -->
    <div class="level-details-section">
      <div class="section-header">Detalhes do Nível</div>
      <v-tabs
        v-model="activeLevelTab"
        background-color="#2C5B40"
        color="#ffdf00"
        show-arrows
      >
        <v-tab
          v-for="(levelData, level) in levelDetails"
          :key="level"
          :value="level"
        >
          Nível {{ level }}({{ levelData.count }} pessoas)
        </v-tab>
      </v-tabs>
      <template v-if="!Object.keys(levelDetails).length">
        <div class="empty-state">
          <v-icon size="64" color="white" class="empty-icon">
            mdi-account-group-outline
          </v-icon>
          <div class="empty-text">Nenhum membro da equipe neste nível</div>
          <div class="empty-subtext">
            Os membros da equipe aparecerão aqui quando forem adicionados
          </div>
        </div>
      </template>
      <v-window v-else v-model="activeLevelTab">
        <v-window-item
          v-for="(levelData, level) in levelDetails"
          :key="level"
          :value="level"
        >
          <v-data-table
            :headers="headers"
            :items="levelData.members"
            :items-per-page="levelData?.members?.length"
            class="elevation-1"
            hide-default-footer
            :no-data-text="'Nenhum dado disponível'"
            @click:row="showMemberDetails"
          >
            <template v-slot:header="{ props }">
              <tr>
                <th
                  v-for="header in props.headers"
                  :key="header.key"
                  :class="header.align"
                >
                  <span class="d-none d-sm-flex">{{ header.title }}</span>
                  <span class="d-flex d-sm-none">{{ header.mobileTitle }}</span>
                </th>
              </tr>
            </template>
            <template v-slot:item.id="{ item }">
              <div>
                <span v-if="item.isNew" class="new-tag">NEW</span>
                {{ item.id }}
              </div>
            </template>
            <template v-slot:item.depositAmount="{ item }">
              R$ {{ item.depositAmount }}
            </template>
            <template v-slot:item.subordinates="{ item }">
              <span class="sub-count">{{ item.subordinates }}</span>
            </template>
          </v-data-table>
        </v-window-item>
      </v-window>
    </div>

    <!-- 詳情彈窗 (顯示下屬列表樣式) -->
    <v-dialog v-model="showDetails" max-width="600">
      <v-card class="details-dialog" rounded="lg">
        <v-card-title class="dialog-title">
          {{ selectedMember ? selectedMember.id : "" }}Subordinado
          <!-- <v-btn icon @click="showDetails = false" class="close-btn">
            <v-icon>mdi-close</v-icon>
          </v-btn> -->
        </v-card-title>
        <v-card-text>
          <div class="subordinate-table" v-if="selectedMember">
            <div class="table-header">
              <div class="col">ID</div>
              <div class="col">Valor da recarga</div>
              <div class="col">Número de telefone</div>
              <div class="col">Valor da recarga</div>
            </div>
            <div
              v-for="subordinate in subordinatesList"
              :key="subordinate.id"
              class="table-row"
            >
              <div class="col">{{ subordinate.id }}</div>
              <div class="col">
                R$ {{ subordinate.recharge_total.toLocaleString("pt-BR") }}
              </div>
              <div class="col">{{ subordinate.phone_number || "-" }}</div>
              <div class="col">
                R$ {{ subordinate.recharge_total.toLocaleString("pt-BR") }}
              </div>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useStore } from "vuex";
import { teamMembers, subordinates } from "@/api/auth";
// import api from "@/api"; // 假設您的下屬 API 在這裡導入

// 定義接口類型
interface TeamMember {
  id: number;
  vip_level: number;
  agent_level: number;
  recharge_total: number;
  register_time: number;
  subordinate_count: number;
}

interface TeamMembersResponse {
  data: TeamMember[];
}

interface TeamMembersParams {
  uuid: number;
  level: number;
}

interface LevelData {
  count: number;
  members: {
    id: number;
    vipLevel: number;
    agentLevel: number;
    depositAmount: string;
    registrationDate: string;
    registrationTime: string;
    subordinates: number;
    isNew: boolean;
  }[];
}

interface GroupedData {
  [key: number]: LevelData;
}

// 新增下屬數據接口 (根據圖片欄位定義)
interface Subordinate {
  id: number;
  recharge_total: number; // 假設是數字，顯示時再格式化
  phone_number?: string; // 假設有電話號碼，可能是可選的
  // 如果圖片中第二個 Valor da recarga 是不同的數據，需要新增對應的字段
}
const store = useStore();
const activeLevelTab = ref(0);
const teamData = ref<TeamMember[]>([]);
const showDetails = ref(false);
const selectedMember = ref<LevelData["members"][0] | null>(null);
// 新增下屬列表 ref
const subordinatesList = ref<Subordinate[]>([]);

const showMemberDetails = (member: LevelData["members"][0]) => {
  selectedMember.value = member;
  // 在這裡呼叫 API 獲取下屬數據
  fetchSubordinates(member.id);
  showDetails.value = true;
};

// 格式化時間戳
const formatDateTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const dateStr = date.toLocaleDateString("pt-BR").replace(/\//g, "/");
  const timeStr = date.toLocaleTimeString("pt-BR");
  return {
    date: dateStr,
    time: timeStr,
  };
};

// 計算總人數
const totalTeamMembers = computed(() => teamData.value.length);

// 處理並分組數據
const levelDetails = computed<GroupedData>(() => {
  const groupedData: GroupedData = {};

  // 初始化所有等級
  for (let i = 1; i <= 3; i++) {
    groupedData[i] = {
      count: 0,
      members: [],
    };
  }
  // 分組數據
  teamData.value.forEach((member) => {
    const level = member.agent_level;
    const formattedTime = formatDateTime(member.register_time);

    groupedData[level]?.members?.push({
      id: member.id,
      vipLevel: member.vip_level,
      agentLevel: member.agent_level,
      depositAmount: member.recharge_total.toLocaleString("pt-BR"),
      registrationDate: formattedTime.date,
      registrationTime: formattedTime.time,
      subordinates: member.subordinate_count,
      isNew: false, // 可以根據需要設置新用戶標記的邏輯
    });

    groupedData[level].count++;
  });

  return groupedData;
});

// 獲取數據
const fetchTeamData = async () => {
  try {
    const response = await teamMembers({
      // uuid: 100000123, // 测试
      uuid: store.state.auth?.user?.uuid,
    });
    if (Array.isArray(response)) {
      teamData.value = response;
    }
  } catch (error) {
    console.error("Error fetching team data:", error);
  }
};

// 獲取下屬數據
const fetchSubordinates = async (memberId: number) => {
  try {
    const response = await subordinates(memberId);
    if (Array.isArray(response)) {
      subordinatesList.value = response;
    }
  } catch (error) {
    console.error("Error fetching subordinates:", error);
    subordinatesList.value = []; // 發生錯誤時清空數據
  }
};

// 添加表格標題配置
const headers = [
  {
    title: "ID",
    key: "id",
    align: "start" as const,
    mobileTitle: "ID",
    sortable: false,
  },
  {
    title: "VIP Nível",
    key: "vipLevel",
    align: "center" as const,
    mobileTitle: "VIP",
    sortable: false,
  },
  {
    title: "Recarregado",
    key: "depositAmount",
    align: "center" as const,
    mobileTitle: "Recarga",
    sortable: false,
  },
  {
    title: "Hora da inscrição",
    key: "registrationDate",
    align: "center" as const,
    mobileTitle: "Hora",
    sortable: false,
  },
  {
    title: "Subordinar",
    key: "subordinates",
    align: "center" as const,
    mobileTitle: "Sub",
    sortable: false,
  },
];

onMounted(() => {
  fetchTeamData();
});
</script>

<style lang="scss" scoped>
.invite-container {
  position: relative;
  padding: 16px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .section-header {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    border-radius: 30px 30px 0 0;
  }

  .team-members-total {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    border-radius: 30px;
    margin-bottom: 10px;
  }

  .level-details-section {
    background: #2C5B40;
    border-radius: 30px;
    margin-bottom: 16px;
    overflow: hidden;

    :deep(.v-data-table) {
      background: transparent;

      .v-data-table__wrapper {
        background: transparent;
        overflow-x: auto;
      }

      .v-data-table-header {
        background: #1d2654;
        th {
          color: white !important;
          font-weight: 500;
          font-size: 14px;
          white-space: nowrap;
          padding: 8px 4px;

          @media screen and (max-width: 600px) {
            font-size: 12px;
            padding: 2px;
          }
        }
      }

      .v-data-table__tbody {
        tr {
          background: transparent;
          color: white;
          transition: background-color 0.3s;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
          }

          td {
            color: white;
            font-size: 14px;
            white-space: nowrap;
            padding: 8px 4px;

            @media screen and (max-width: 600px) {
              font-size: 12px;
              padding: 2px;
            }
          }
        }
      }

      .v-data-footer {
        background: transparent;
        color: white;
      }
    }
  }
}

.new-tag {
  background-color: red;
  color: white;
  padding: 2px 4px;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 10px;
  vertical-align: middle;
}

.sub-count {
  color: red;
  font-weight: bold;
}

@media screen and (max-width: 768px) {
  .invite-container {
    padding: 12px;

    .section-header {
      font-size: 14px;
      padding: 12px;
    }

    .level-details-section {
      :deep(.v-data-table) {
        .v-data-table-header th,
        .v-data-table__tbody td {
          font-size: 12px;
        }
      }
    }
    :deep(.v-slide-group__content) {
      .v-tab.v-tab.v-btn {
        font-size: 12px ;
        padding: 0 4px;
        height: calc(var(--v-btn-height) + 0px);
      }
    }
    .v-data-table {
      :deep(th),
      :deep(td) {
        font-size: 12px !important;
        padding: 2px !important;
      }
    }
  }
}

@media screen and (max-width: 375px) {
  .invite-container {
    padding: 8px;

    .level-details-section {
      :deep(.v-data-table) {
        .v-data-table-header th,
        .v-data-table__tbody td {
          font-size: 11px;
          padding: 6px;
        }
      }
    }
  }
}

.details-dialog {
  background: #2C5B40;
  color: white;
  /* border-radius: 20px; */

  .dialog-title {
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    font-weight: bold;
    font-size: 18px;
    position: relative;

    .close-btn {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      color: white;
    }
  }

  .subordinate-table {
    padding: 16px;
    overflow-x: auto;

    .table-header,
    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 10px;
      padding: 12px 8px;
      text-align: center;
      font-size: 14px;
    }

    .table-header {
      color: white;
      font-weight: 500;
      background: #1d2654;
    }

    .table-row {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: white;

      .col:nth-child(1) {
        text-align: left;
      }
      .col:nth-child(2) {
        text-align: center;
      }
      .col:nth-child(3) {
        text-align: center;
      }
      .col:nth-child(4) {
        text-align: right;
      }
    }
  }

  .details-content {
    /* 保留樣式定義，但模板中不再使用 */
    padding: 16px;

    .detail-item {
      display: flex;
      margin-bottom: 12px;
      font-size: 14px;

      .label {
        color: #ffdf00;
        width: 180px;
        flex-shrink: 0;
      }

      .value {
        color: white;
      }
    }
  }
}

// Use deep selector to target the actual v-card element
:deep(.v-card) {
  border-radius: 20px !important; // Use !important to ensure it overrides Vuetify's default styles if necessary
}

.table-row {
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.empty-data {
  text-align: center;
  padding: 20px;
  color: white;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  margin: 10px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 30px;
  margin: 20px;

  .empty-icon {
    margin-bottom: 16px;
    opacity: 0.7;
  }

  .empty-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    text-align: center;
  }

  .empty-subtext {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    text-align: center;
  }
}
</style>
