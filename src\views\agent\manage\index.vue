<template>
  <div
    class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"
  >
    <AgentStats ref="agentStatsRef" :can-manage-agent-level="manageAgent" />
    <AgentSearch
      v-model:model="searchParams"
      @reset="resetSearchParams"
      @search="getDataByPage"
    />

    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>代理商管理</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :isNoDelete="true"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @add="handleAdd"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="agent_id"
          @selection-change="checkedRowKeys = $event.map((row: AgentItem) => row.agent_id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <AgentOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="handleRefresh"
      />
      <RegisteredUsersDrawer
        v-model:visible="registeredUsersDrawerVisible"
        :agent-id="selectedAgentId"
        @closed="selectedAgentId = null"
      />
      <RechargeUsersDialog
        v-model:visible="rechargeUsersDialogVisible"
        :agent-id="selectedAgentId"
        @closed="selectedAgentId = null"
      />
      <AgentLevelDialog
        v-model:visible="agentLevelDialogVisible"
        :agent-id="selectedAgentId"
        @closed="selectedAgentId = null"
      />
      <SubordinateUsersDrawer
        v-model:visible="subordinateUsersDrawerVisible"
        :agent-id="selectedAgentId"
        @closed="selectedAgentId = null"
      />
      <AccountListDialog
        v-model:visible="accountListDialogVisible"
        :agent-id="accountListAgentId"
        @closed="accountListAgentId = null"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { ElButton, ElPopconfirm, ElTag } from "element-plus";
import {
  fetchGetAgentList,
  fetchUpdateAgentStatus,
  fetchGetAgentUserRole,
  deleteSubAgents,
} from "@/service/api/agent";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { $t } from "@/locales";
import AgentSearch from "./modules/agent-search.vue";
import AgentOperateDrawer from "./modules/agent-operate-drawer.vue";
import RegisteredUsersDrawer from "./modules/registered-users-drawer.vue";
import RechargeUsersDialog from "./modules/recharge-users-dialog.vue";
import AgentLevelDialog from "./modules/agent-level-dialog.vue";
import SubordinateUsersDrawer from "./modules/subordinate-users-drawer.vue";
import AccountListDialog from "./modules/account-list-dialog.vue";
import AgentStats from "./modules/AgentStats.vue";
import type { AgentItem } from "@/service/api/agent";
import type { EnableStatus } from "@/typings/api";
import { onMounted, ref, computed } from "vue";
import moment from "moment";

defineOptions({ name: "AgentManage" });

const canManageAgentLevel = ref(false); // 是否為平台角色
const manageAgent = ref(false);

const columns = computed(() => [
  //  { type: 'selection', width: 48 },
  { prop: "index", label: $t("common.index"), width: 64 },
  {
    prop: "agent_id",
    label: "代理商ID",
    minWidth: 130,
  },
  { prop: "agent_name", label: "代理商名称", minWidth: 120 },
  {
    prop: "registered_users",
    label: "注册人数",
    minWidth: 100,
    formatter: (row: AgentItem) => (
      <span
        style="color: blue; cursor: pointer;"
        onClick={() => handleRegisteredUsersClick(row.agent_id)}
      >
        {row.registered_users}
      </span>
    ),
  },
  { prop: "recharge_amount", label: "充值金额", minWidth: 100 },
  {
    prop: "recharge_users",
    label: "充值人数",
    minWidth: 100,
    formatter: (row: AgentItem) => (
      <span
        style="color: blue; cursor: pointer;"
        onClick={() => handleRechargeUsersClick(row.agent_id)}
      >
        {row.recharge_users}
      </span>
    ),
  },
  {
    prop: "number_of_subordinates",
    label: "下级数量",
    minWidth: 100,
  },
  ...(canManageAgentLevel.value
    ? [
        {
          prop: "agent_level_type_name",
          label: "代理层级",
          minWidth: 100,
          formatter: (row: AgentItem) => (
            <span
              style="color: blue; cursor: pointer;"
              onClick={() => handleAgentLevelClick(row.agent_id)}
            >
              {row.agent_level_type_name}
            </span>
          ),
        },
      ]
    : []),
  {
    prop: "created_at",
    label: "创建时间",
    minWidth: 180,
    formatter: (row: AgentItem) => (
      <span>{moment(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</span>
    ),
  },
  // {
  //   prop: "number_of_accounts",
  //   label: "账号数量",
  //   minWidth: 100,
  // },
  {
    prop: "status",
    label: "状态",
    width: 100,
    formatter: (row: AgentItem) => {
      if (row.status === undefined) {
        return "";
      }

      const tagMap: Record<EnableStatus, "success" | "danger"> = {
        1: "success",
        2: "danger",
      };
      const label = ["启用", "禁用"][Number(row.status) - 1];
      return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
    },
  },
  {
    width: 180,
    prop: "operate",
    label: $t("common.operate"),
    align: "center",
    fixed: "right",
    formatter: (row: AgentItem) => (
      <div class="flex-center">
        <ElButton
          type="primary"
          plain
          size="small"
          onClick={() => edit(row.agent_id)}
        >
          {$t("common.edit")}
        </ElButton>
        <ElPopconfirm
          title={row.status === 1 ? "确认禁用该代理商？" : "确认启用该代理商？"}
          onConfirm={() =>
            handleStatusChange(row.agent_id, row.status === 1 ? 2 : 1)
          }
        >
          {{
            reference: () => (
              <ElButton
                type={row.status === 1 ? "warning" : "success"}
                plain
                size="small"
              >
                {row.status === 1 ? "禁用" : "启用"}
              </ElButton>
            ),
          }}
        </ElPopconfirm>
        <ElPopconfirm
          title={$t("common.confirmDelete")}
          onConfirm={() => handleDelete(row.agent_id)}
        >
          {{
            reference: () => (
              <ElButton
                v-show={!row.number_of_subordinates}
                type="danger"
                plain
                size="small"
              >
                {$t("common.delete")}
              </ElButton>
            ),
          }}
        </ElPopconfirm>
      </div>
    ),
  },
]);

const {
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable<any>({
  apiFn: fetchGetAgentList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    agent_name: undefined,
    status: undefined,
  },
  columns: () => columns.value,
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate<any>(data, getData, "agent_id");

// 註冊人員列表彈窗相關狀態
const registeredUsersDrawerVisible = ref(false);
const selectedAgentId = ref<number | null>(null);

// 充值人員列表對話框相關狀態
const rechargeUsersDialogVisible = ref(false);

// 代理層級對話框相關狀態
const agentLevelDialogVisible = ref(false);

// 下级人員列表抽屜相關狀態
const subordinateUsersDrawerVisible = ref(false);

const accountListDialogVisible = ref(false);
const accountListAgentId = ref<number | null>(null);

// 获取角色权限 平台/代理

function fetchGetAgentUserRoleBool() {
  fetchGetAgentUserRole({}).then((res) => {
    canManageAgentLevel.value = res.data.data.role_id === 1 ? true : false;
    manageAgent.value = res.data.data.role_id > 1 ? false : true;
  });
}

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的代理商");
    return;
  }
  await deleteSubAgents({ agent_ids: checkedRowKeys.value });
  getData();
}

async function handleStatusChange(id: number, value: EnableStatus) {
  const { error } = await fetchUpdateAgentStatus({
    agent_id: id,
    status: value,
  });
  if (!error) {
    window.$message?.success("状态更新成功");
    getData();
  }
}

async function handleDelete(id: number) {
  console.log("删除代理商 ID:", id);
  await deleteSubAgents({ agent_ids: [id] });
  getData();
}

function edit(id: number) {
  handleEdit(id);
}

// 處理點擊註冊人數
function handleRegisteredUsersClick(agentId: number) {
  selectedAgentId.value = agentId;
  registeredUsersDrawerVisible.value = true;
}

// 處理點擊充值人數
function handleRechargeUsersClick(agentId: number) {
  selectedAgentId.value = agentId;
  rechargeUsersDialogVisible.value = true;
}

// 處理點擊代理層級
function handleAgentLevelClick(agentId: number) {
  selectedAgentId.value = agentId;
  agentLevelDialogVisible.value = true;
}
const agentStatsRef = ref()
// 新增后刷新数据
const handleRefresh =()=>{
 getData()
 agentStatsRef.value.fetchStatsData()
}

// 處理點擊下级數量
function handleSubordinateUsersClick(agentId: number) {
  selectedAgentId.value = agentId;
  subordinateUsersDrawerVisible.value = true;
}

function handleAccountListClick(agentId: number) {
  accountListAgentId.value = agentId;
  accountListDialogVisible.value = true;
}

onMounted(() => {
  fetchGetAgentUserRoleBool();
});
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 0;
}
</style>
