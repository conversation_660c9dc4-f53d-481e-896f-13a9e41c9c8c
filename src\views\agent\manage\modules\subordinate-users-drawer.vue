<template>
  <ElDialog
    v-model="drawerVisible"
    title="下级人员列表"
    width="50%"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="h-full flex-col-stretch">
      <!-- 搜索区域 -->
      <div class="search-area mb-20px">
        <ElInput
          v-model="searchForm.agentId"
          placeholder="代理商ID"
          class="w-200px"
          clearable
        />
        <ElInput
          v-model="searchForm.agentName"
          placeholder="代理商名称"
          class="w-200px ml-10px"
          clearable
        />
        <ElButton type="primary" class="ml-10px" :loading="loading" @click="handleSearch">搜索</ElButton>
        <ElButton class="ml-10px" :loading="loading" @click="handleReset">重置</ElButton>
      </div>

      <ElTable v-loading="loading" :data="tableData" height="calc(100% - 100px)">
        <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
      </ElTable>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="total"
          layout="total,prev,pager,next,sizes"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, watch, h } from "vue";
import type { AgentItem } from "@/service/api/agent";
import { fetchGetSubordinateList } from "@/service/api/agent";
import { ElTag } from "element-plus";

const props = defineProps<{
  visible: boolean;
  agentId: number | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "closed"): void;
}>();

const drawerVisible = ref(false);
const loading = ref(false);
const tableData = ref<AgentItem[]>([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 搜索表单
const searchForm = ref({
  agentId: "",
  agentName: "",
});

const columns = [
  { prop: "agent_id", label: "代理商ID", minWidth: 120 },
  { prop: "agent_name", label: "代理商名称", minWidth: 120 },
  { prop: "registered_users", label: "注册人数", minWidth: 100 },
  { prop: "recharge_amount", label: "充值金额", minWidth: 100 },
  { prop: "recharge_users", label: "充值人数", minWidth: 100 },
  { prop: "created_at", label: "创建时间", minWidth: 180 },
  {
    prop: "status",
    label: "状态",
    width: 100,
    formatter: (row: AgentItem) => {
      const tagMap: Record<string, "success" | "danger"> = {
        "1": "success",
        "2": "danger",
      };
      const label = ["", "禁用", "启用"][Number(row.status) - 1];
      const type = tagMap[row.status as keyof typeof tagMap] || "success";
      return h(ElTag, { type }, () => label);
    },
  },
];

watch(
  () => props.visible,
  (val) => {
    drawerVisible.value = val;
    if (val && props.agentId) {
      fetchData();
    }
  },
);

watch(
  () => drawerVisible.value,
  (val) => {
    emit("update:visible", val);
    if (!val) {
      emit("closed");
    }
  },
);

async function fetchData() {
  if (!props.agentId) return;

  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      agent_id: props.agentId,
      search_agent_id: searchForm.value.agentId,
      search_agent_name: searchForm.value.agentName,
    };

    const response = await fetchGetSubordinateList(params);
    if (response.data) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error("Failed to fetch subordinate list:", error);
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  currentPage.value = 1;
  fetchData();
}

function handleReset() {
  searchForm.value = {
    agentId: "",
    agentName: "",
  };
  currentPage.value = 1;
  fetchData();
}

function handleCurrentChange(val: number) {
  currentPage.value = val;
  fetchData();
}

function handleSizeChange(val: number) {
  pageSize.value = val;
  currentPage.value = 1;
  fetchData();
}

function handleClose() {
  drawerVisible.value = false;
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  margin-bottom: 0;
}

.search-area {
  display: flex;
  align-items: center;
}
</style>
