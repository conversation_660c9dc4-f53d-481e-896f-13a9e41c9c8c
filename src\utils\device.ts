/**
 * 设备指纹生成器 - 独立版本
 * 支持跨浏览器一致的设备ID生成
 * 适用于不同项目中的设备指纹需求
 */

// 类型定义
export interface FingerprintResult {
  deviceId: string;
  browserId: string;
  isStable: boolean;
}

export interface IOSStableFeatures {
  modelClass: string;
  screenDimension: number;
  iosVersion: string;
}

export interface MacOSStableFeatures {
  cores: number;
  language: string;
  osVersion: string;
  deviceType: string;
}

export interface DeviceFeatures {
  osFamily: string;
  deviceClass: string;
  cores: number;
  memory: number;
  colorDepth: number;
  timezone: string;
  iosStableFeatures: IOSStableFeatures | null;
  macOSStableFeatures: MacOSStableFeatures | null;
}

/**
* 设备指纹生成类
* 能够生成跨浏览器一致的设备ID
*/
export class DeviceFingerprint {
  private fingerprint: string | null;

  constructor() {
      this.fingerprint = null;
  }

  /**
   * 生成设备指纹（包含稳定指纹和浏览器指纹）
   */
  public async generate(): Promise<FingerprintResult> {
      try {
          // 收集设备特征
          const features = await this.collectFeatures();
          
          // 生成设备ID
          const deviceId = await this.hashFeatures(features);
          
          // 生成浏览器ID（使用更多浏览器特定特征）
          const browserId = await this.generateBrowserId(features);
          
          this.fingerprint = deviceId;
          
          return {
              deviceId: deviceId,
              browserId: browserId,
              isStable: true
          };

      } catch (error) {
          console.warn('生成设备指纹时出错:', error);
          return await this.generateFallbackFingerprint();
      }
  }

  /**
   * 收集设备特征
   */
  private async collectFeatures(): Promise<DeviceFeatures> {
      // 收集设备硬件特征
      const osFamily = this.getOSFamily();
      const cores = navigator.hardwareConcurrency || 0;
      const memory = (navigator as any).deviceMemory || 0;
      const colorDepth = screen.colorDepth || 0;
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || '';
      const deviceClass = this.getDeviceClass();
      
      // 获取iOS设备特有的稳定特征
      const iosStableFeatures = this.getIOSStableFeatures();
      
      // 获取macOS设备特有的稳定特征
      const macOSStableFeatures = this.getMacOSStableFeatures();
      
      // 组合特征
      return {
          osFamily,
          deviceClass,
          cores,
          memory,
          colorDepth,
          timezone,
          iosStableFeatures,
          macOSStableFeatures
      };
  }

  /**
   * 获取操作系统家族
   */
  private getOSFamily(): string {
      const ua = navigator.userAgent.toLowerCase();
      const platform = (navigator.platform || '').toLowerCase();
      
      if (ua.includes('iphone') || ua.includes('ipad') || ua.includes('ipod') || (ua.includes('mac') && ua.includes('mobile'))) {
          return 'ios';
      }
      
      if (ua.includes('mac os') || platform.includes('mac')) {
          return 'mac';
      }
      
      if (ua.includes('windows') || platform.includes('win')) {
          return 'windows';
      }
      
      if (ua.includes('android')) {
          return 'android';
      }
      
      if (ua.includes('linux') || platform.includes('linux')) {
          return 'linux';
      }
      
      return 'unknown';
  }

  /**
   * 获取设备类型
   */
  private getDeviceClass(): string {
      const ua = navigator.userAgent.toLowerCase();
      
      // 检测移动设备
      if (
          ua.includes('iphone') || 
          ua.includes('ipod') || 
          (ua.includes('android') && ua.includes('mobile'))
      ) {
          return 'mobile';
      }
      
      // 检测平板设备
      if (
          ua.includes('ipad') || 
          (ua.includes('android') && !ua.includes('mobile')) ||
          ua.includes('tablet')
      ) {
          return 'tablet';
      }
      
      // 默认为桌面设备
      return 'desktop';
  }

  /**
   * 获取iOS设备特有的稳定特征
   */
  private getIOSStableFeatures(): IOSStableFeatures | null {
      // 仅适用于iOS设备
      if (this.getOSFamily() !== 'ios') {
          return null;
      }
      
      // 获取屏幕物理尺寸特征
      const screenWidth = screen.width * window.devicePixelRatio;
      const screenHeight = screen.height * window.devicePixelRatio;
      const maxScreenDimension = Math.max(screenWidth, screenHeight);
      
      // 判断是iPhone还是iPad
      const isIpad = navigator.userAgent.includes('iPad') || 
          (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
      
      // 判断设备型号范围（基于屏幕尺寸）
      let modelClass = '';
      if (isIpad) {
          modelClass = 'ipad';
      } else {
          // iPhone屏幕尺寸分类
          if (maxScreenDimension <= 1136) {
              modelClass = 'iphone-small'; // iPhone 5/5S/SE
          } else if (maxScreenDimension <= 1334) {
              modelClass = 'iphone-medium'; // iPhone 6/7/8
          } else if (maxScreenDimension <= 1920) {
              modelClass = 'iphone-large'; // iPhone Plus models
          } else {
              modelClass = 'iphone-xlarge'; // iPhone X and newer
          }
      }
      
      // 获取iOS版本的主要版本号
      let iosVersion = '';
      const match = navigator.userAgent.match(/OS (\d+)_/);
      if (match && match[1]) {
          iosVersion = match[1];
      }
      
      return {
          modelClass,
          screenDimension: maxScreenDimension,
          iosVersion
      };
  }

  /**
   * 获取macOS设备特有的稳定特征
   */
  private getMacOSStableFeatures(): MacOSStableFeatures | null {
      // 仅适用于macOS设备
      if (this.getOSFamily() !== 'mac') {
          return null;
      }
      
      // 获取CPU核心数 - 这是相对稳定的硬件特征
      const cores = navigator.hardwareConcurrency || 0;
      
      // 获取系统语言 - 相对稳定的系统特征
      const language = navigator.language || '';
      
      // 获取操作系统版本信息
      let osVersion = '';
      const match = navigator.userAgent.match(/Mac OS X (\d+[._]\d+)/);
      if (match && match[1]) {
          osVersion = match[1].replace(/_/g, '.');
      }
      
      // 获取设备类型 - 是否是笔记本电脑
      let deviceType = 'unknown';
      if (navigator.userAgent.includes('MacBook')) {
          deviceType = 'laptop';
      } else if (navigator.userAgent.includes('iMac')) {
          deviceType = 'desktop';
      } else if (navigator.userAgent.includes('Mac Mini')) {
          deviceType = 'mini';
      } else if (navigator.userAgent.includes('Mac Pro')) {
          deviceType = 'pro';
      } else {
          // 默认情况下，尝试通过电池API检测是否为笔记本电脑
          if ('getBattery' in navigator) {
              deviceType = 'laptop'; // 有电池API，可能是笔记本电脑
          } else {
              deviceType = 'desktop'; // 无电池API，可能是台式机
          }
      }
      
      return {
          cores,
          language,
          osVersion,
          deviceType
      };
  }

  /**
   * 生成设备特征哈希
   */
  private async hashFeatures(features: DeviceFeatures): Promise<string> {
      // 为不同设备类型添加特定标识符
      const osFamily = features.osFamily;
      const deviceClass = features.deviceClass;
      
      // 构建设备特征字符串，确保跨浏览器一致性
      let deviceFeatureStr = '';
      
      // iOS设备特殊处理 - 使用更稳定的特征
      if (osFamily === 'ios' && features.iosStableFeatures) {
          // 使用iOS特有的稳定特征，避免使用浏览器相关特征
          const { modelClass, screenDimension, iosVersion } = features.iosStableFeatures;
          deviceFeatureStr = `ios-${modelClass}-${screenDimension}-${iosVersion}`;
      }
      // macOS设备特殊处理 - 使用更稳定的特征，避免使用显示器相关特征
      else if (osFamily === 'mac' && features.macOSStableFeatures) {
          // 使用macOS特有的稳定特征
          const { cores, osVersion, deviceType } = features.macOSStableFeatures;
          deviceFeatureStr = `mac-${deviceType}-${cores}-${osVersion}`;
      }
      // Android设备特殊处理
      else if (osFamily === 'android') {
          // 使用屏幕尺寸和硬件特征
          const screenHeight = Math.max(screen.width, screen.height);
          deviceFeatureStr = `android-${deviceClass}-${screenHeight}-${features.cores}`;
      }
      // Windows设备特殊处理
      else if (osFamily === 'windows') {
          // 使用硬件特征
          const cores = features.cores;
          deviceFeatureStr = `windows-${deviceClass}-${cores}`;
      }
      // 其他设备类型处理
      else {
          deviceFeatureStr = `${osFamily}-${deviceClass}`;
      }
      
      // 添加设备类型前缀
      const deviceTypePrefix = deviceClass.charAt(0).toUpperCase();
      
      // 组合设备特征字符串，排除显示器和浏览器特定特征
      const featuresStr = `${deviceTypePrefix}-${deviceFeatureStr}-${features.colorDepth}-${features.timezone.split('/')[0]}`;
      
      // 生成SHA-256哈希
      try {
          // 使用Web Crypto API
          const encoder = new TextEncoder();
          const data = encoder.encode(featuresStr);
          const hashBuffer = await crypto.subtle.digest('SHA-256', data);
          
          // 转换为十六进制字符串
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          
          return hashHex;
      } catch (e) {
          // 如果Web Crypto API不可用，使用简单的字符串作为设备ID
          console.warn('Web Crypto API不可用，使用备用方法');
          
          // 使用设备特征字符串作为ID
          return `${deviceTypePrefix}-${deviceFeatureStr}`;
      }
  }

  /**
   * 生成浏览器ID（包含更多浏览器特定特征）
   */
  private async generateBrowserId(features: DeviceFeatures): Promise<string> {
      // 收集浏览器特定特征
      const browserFeatures = {
          userAgent: navigator.userAgent,
          vendor: navigator.vendor || '',
          product: navigator.product || '',
          screenResolution: `${screen.width}x${screen.height}`,
          screenAvailable: `${screen.availWidth}x${screen.availHeight}`,
          devicePixelRatio: window.devicePixelRatio || 1,
          maxTouchPoints: navigator.maxTouchPoints || 0,
          language: navigator.language,
          languages: navigator.languages ? navigator.languages.join(',') : '',
          cookieEnabled: navigator.cookieEnabled,
          doNotTrack: (navigator as any).doNotTrack || '',
          hardwareConcurrency: navigator.hardwareConcurrency || 0,
          deviceMemory: (navigator as any).deviceMemory || 0,
          platform: navigator.platform,
          onLine: navigator.onLine,
          webdriver: (navigator as any).webdriver || false
      };
      
      // 生成浏览器特征字符串
      const browserStr = JSON.stringify(browserFeatures);
      
      // 生成哈希
      try {
          const encoder = new TextEncoder();
          const data = encoder.encode(browserStr);
          const hashBuffer = await crypto.subtle.digest('SHA-256', data);
          
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          
          return hashHex;
      } catch (e) {
          // 备用方法
          return `browser-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      }
  }

  /**
   * 生成后备指纹
   */
  private async generateFallbackFingerprint(): Promise<FingerprintResult> {
      const ua = navigator.userAgent;
      const platform = navigator.platform;
      const timestamp = new Date().getTime();
      const random = Math.random().toString(36).substring(2, 15);
      
      const fallbackId = `fallback-${timestamp}-${random}`;
      
      return {
          deviceId: fallbackId,
          browserId: fallbackId,
          isStable: false
      };
  }

  /**
   * 获取当前指纹
   */
  public getFingerprint(): string | null {
      return this.fingerprint;
  }

  /**
   * 获取操作系统
   */
  public getOS(): string {
      const ua = navigator.userAgent;
      
      if (/Windows/.test(ua)) return 'Windows';
      if (/Macintosh|Mac OS X/.test(ua)) return 'macOS';
      if (/Linux/.test(ua)) return 'Linux';
      if (/Android/.test(ua)) return 'Android';
      if (/iPhone|iPad|iPod/.test(ua)) return 'iOS';
      
      return 'Unknown';
  }

  /**
   * 获取设备类型
   */
  public getDeviceType(): string {
      const deviceClass = this.getDeviceClass();
      
      switch (deviceClass) {
          case 'mobile':
              return '移动设备';
          case 'tablet':
              return '平板设备';
          case 'desktop':
              return '桌面设备';
          default:
              return '未知设备';
      }
  }
}

// 工厂函数
export function createDeviceFingerprint(): DeviceFingerprint {
  return new DeviceFingerprint();
}

// 便捷函数
export async function generateDeviceFingerprint(): Promise<FingerprintResult> {
  const fingerprint = new DeviceFingerprint();
//   console.log(await fingerprint.generate())
  return await fingerprint.generate();
}

// 默认导出
export default DeviceFingerprint; 