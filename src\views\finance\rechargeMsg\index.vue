<script setup lang="tsx">
import { ref } from "vue";
import { ElTag, ElMessage } from "element-plus";
import { getDepositList, getDepositUserRecords } from "@/service/api/deposit";
import { useTable } from "@/hooks/common/table";
import type { DepositListParams, DepositListResponse } from "@/typings/deposit";
import type { TableColumn } from "@/typings/table";
import RechargeMsgSearch from "./modules/recharge-msg-search.vue";
import {
  getFirstDepositBonusConfig,
  setFirstDepositBonusConfig,
  type FirstDepositBonusConfig,
} from "@/service/api/wallet";
import moment from "moment";

// 首充配置弹窗相关
const firstRechargeDialogVisible = ref(false);
const firstRechargeConfig = ref<FirstDepositBonusConfig>({
  amount: 0,
  bonus_type: "digital",
  status: 1,
});
const firstRechargeLoading = ref(false);

async function handleFirstRechargeConfig() {
  firstRechargeDialogVisible.value = true;
  firstRechargeLoading.value = true;
  try {
    const res = await getFirstDepositBonusConfig();
    if (res.data && res.data.data) {
      firstRechargeConfig.value = { ...res.data.data };
    }
  } finally {
    firstRechargeLoading.value = false;
  }
}

async function handleSaveFirstRechargeConfig() {
  firstRechargeLoading.value = true;
  try {
    const params = {
      ...firstRechargeConfig.value,
      amount: Math.round(Number(firstRechargeConfig.value.amount)),
    };
    const res = await setFirstDepositBonusConfig(params);
    if (res.data) {
      ElMessage.success("首充奖励配置保存成功");
      firstRechargeDialogVisible.value = false;
    } else {
      ElMessage.error(res.data?.message || "保存失败");
    }
  } finally {
    firstRechargeLoading.value = false;
  }
}

// Table Hook configuration
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable<any>({
  apiFn: getDepositUserRecords,
  apiParams: {
    page: 1,
    size: 10,
    start_time: undefined,
    end_time: undefined,
    payment_method: undefined,
    uuid: undefined,
    transaction_no: undefined,
    is_recharge: undefined,
  },
  columns: () => [
    { prop: "index", label: "序号", minWidth: 64 },
    { prop: "uuid", label: "用户ID", minWidth: 120 },
    {
      prop: "recharge_amount",
      label: "充值金额",
      minWidth: 120,
      formatter: (row) =>
        `R$ ${(row.recharge_amount / 100)?.toFixed(2) || "0.00"}`,
    },
    {
      prop: "status",
      label: "状态",
      minWidth: 100,
      formatter: (row: TableItem) => (
        <ElTag
          type={
            row.status === "completed"
              ? "success"
              : row.status === "pending"
                ? "warning"
                : "danger"
          }
        >
          {row.status === "completed"
            ? "已完成"
            : row.status === "pending"
              ? "处理中"
              : "失败"}
        </ElTag>
      ),
    },
    { prop: "order_no", label: "订单号", minWidth: 140 },
    { prop: "transaction_no", label: "交易号", minWidth: 140 },
    { prop: "payment_method", label: "支付方式", minWidth: 120 },
    { prop: "cpf", label: "充值卡号", minWidth: 120 },
    {
      prop: "is_recharge",
      label: "充值状态",
      minWidth: 100,
      formatter: (row: any) => {
        if (row.status == "completed" && row.number_recharges == 1) {
          return <span style="color: red">首次充值</span>;
        } else if (row.number_recharges >= 1) {
          return "充值";
        } else if (row.number_recharges == 0) {
          return "未充值";
        }
      },
    },
    { prop: "number_recharges", label: "充值次数", minWidth: 120 },
    {
      prop: "created_at",
      label: "创建时间",
      minWidth: 180,
      formatter: (row: any) => {
        return moment(row.created_at).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  ],
});

function handleSearch() {
  // 拆分日期
  if (searchParams.created_at) {
    searchParams.start_time = searchParams.created_at[0];
    searchParams.end_time = searchParams.created_at[1];
  } else {
    searchParams.start_time = undefined;
    searchParams.end_time = undefined;
  }
  delete searchParams.created_at;
  getDataByPage();
}

function handleReset() {
  resetSearchParams();
  // handleSearch();
}

defineOptions({ name: "DepositRecord" });
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <RechargeMsgSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @refresh="getData"
        >
          <!-- <ElButton type="primary" plain @click="handleFirstRechargeConfig"><template #icon><icon-ant-design-setting-outlined class="text-icon"></icon-ant-design-setting-outlined></template>首充配置
</ElButton> -->
          <span style="width: 1px; height: 35px; background: #e5e6eb"></span>
        </TableHeaderOperation>
      </template>
    </RechargeMsgSearch>

    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>

    <!-- 首充奖励配置弹窗 -->
    <ElDialog
      v-model="firstRechargeDialogVisible"
      title="首充奖励配置"
      width="400px"
    >
      <ElForm
        :model="firstRechargeConfig"
        label-width="100px"
        v-loading="firstRechargeLoading"
      >
        <ElFormItem label="奖励金额">
          <ElInput
            v-model="firstRechargeConfig.amount"
            type="number"
            min="0"
            placeholder="请输入奖励金额（分）"
          >
            <template #append>分</template>
          </ElInput>
          <div class="text-12px text-gray-500 mt-4px">1元=100分</div>
        </ElFormItem>
        <ElFormItem label="奖励类型">
          <ElSelect
            v-model="firstRechargeConfig.bonus_type"
            placeholder="请选择奖励类型"
            disabled
          >
            <ElOption label="赠金" value="digital" />
            <ElOption label="现金" value="cash" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="状态">
          <ElSwitch
            v-model="firstRechargeConfig.status"
            :active-value="1"
            :inactive-value="0"
            disabled
          />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="firstRechargeDialogVisible = false">取消</ElButton>
        <ElButton
          type="primary"
          :loading="firstRechargeLoading"
          @click="handleSaveFirstRechargeConfig"
          >保存</ElButton
        >
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;

  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
