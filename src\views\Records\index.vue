<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-20 15:49:37
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-21 21:38:54
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\views\Records\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <v-container class="deposit-container" max-width="500">
    <!-- 充值信息 -->
    <div class="deposit-form pa-4">
      <!-- 金额输入 -->
      <div class="amount-input">
        <v-text-field
          v-model="selectedAmount"
          variant="outlined"
          :disabled="selection"
          :placeholder="'Dep<PERSON>ito mínimo&nbsp;' + limit_min"
          :error="!!amountError"
          :error-messages="amountError"
        >
          <template #prepend-inner>
            <span>{{
              userInfo.paymentMethod === "cashpay" ? "R$" : "USDT"
            }}</span>
          </template>
          <template #append-inner>
            <div class="sorteio" v-show="selection">
              <span>Sorteio +R${{ extraAmount }}</span>
            </div>
          </template>
        </v-text-field>
        <v-select
          v-model="userInfo.paymentMethod"
          persistent-hint
          label=""
          :items="payOptions"
          variant="outlined"
          item-title="title"
          item-value="value"
          style="border-radius: 8px"
          @update:model-value="handleUpdate"
        ></v-select>

        <template v-if="userInfo.paymentMethod === 'cashpay'">
          <v-text-field
            v-model="merchantInfo.name"
            variant="outlined"
            :disabled="merchantInfoIsEdit"
            :error="!!nameError"
            :error-messages="nameError"
          >
            <template #prepend-inner>
              <span>Nome</span>
            </template>
          </v-text-field>
          <v-text-field
            v-model="merchantInfo.cpf"
            variant="outlined"
            :disabled="merchantInfoIsEdit"
            :error="!!cpfError"
            :error-messages="cpfError"
          >
            <template #prepend-inner>
              <span>CPF</span>
            </template>
          </v-text-field>
        </template>
        <!-- <v-text-field
          v-else-if="userInfo.paymentMethod === 'coingate'"
          v-model="merchantInfo.coingate"
          variant="outlined"
          disabled
        >
          <template #prepend-inner>
            <span>Coingate</span>
          </template>
        </v-text-field> -->
      </div>
    </div>

    <v-item-group
      selected-class="bg-select"
      class="bg-group"
      v-model="selection"
      v-show="userInfo.paymentMethod === 'cashpay'"
    >
      <v-container class="deposit-form">
        <v-row>
          <v-col
            v-for="n in rechargeLists"
            :key="n"
            cols="6"
            md="4"
            class="deposit-form-col"
          >
            <v-item
              v-slot="{ isSelected, selectedClass, toggle }"
              :value="n.id"
            >
              <v-card
                :class="['d-flex align-center', selectedClass]"
                height="100"
                :disabled="userInfo.paymentMethod !== 'cashpay'"
                @click="toggle"
              >
                <div class="recharge-package">
                  <div v-if="n.cash_amount" class="recharge-package-price">
                    <text class="">+{{ n.cash_amount / 100 }} </text>
                    <text>Dinheiro</text>
                  </div>
                  <div v-if="n.bonus_amount" class="recharge-package-price">
                    <text class="">+{{ Number(n.bonus_amount) / 100 }} </text>
                    <text>Bônus</text>
                  </div>
                  <div v-if="n.bet_cash_amount" class="recharge-package-price">
                    <text class=""
                      >+{{ Number(n.bet_cash_amount) / 100 }}
                    </text>
                    <text>Dinheiro especial</text>
                  </div>
                  <text class="bonus-amount"
                    >{{ "R$" }}{{ Number(n.recharge_amount) / 100 }}</text
                  >
                  <!-- <div class="bonus-chances">{{ n.chances }} chances</div> -->
                </div>
              </v-card>
            </v-item>
          </v-col>
        </v-row>
      </v-container>
    </v-item-group>
    <!-- 提交按钮 -->
    <v-btn
      block
      class="submit-btn text-none text-subtitle-1 mt-2 mb-4"
      color="primary"
      :loading="isLoading"
      @click="handleSubmit"
    >
      Enviar
    </v-btn>

    <!-- <div class="withdrawal-hint" v-if="!merchantInfoIsEdit">
      <div class="hint-text">
        A operação de saque só aceita o CPF vinculado à primeira recarga
      </div>
    </div> -->
  </v-container>
</template>

<script setup lang="ts">
import { ref, onUnmounted, onMounted, watchEffect } from "vue";
import { showSuccess, showError } from "@/utils/toast";
import {
  submitDeposit,
  queryDepositStatus,
  queryUserBinding,
  type DepositParams,
  type DepositResponse,
  getPaychannels,
  getRechargeLists,
} from "@/api/wallet";
import { useRouter } from "vue-router";
import { useStore } from "vuex";

const store = useStore();
const router = useRouter();
const selectedAmount = ref();
const extraAmount = ref();
const selectedIndex = ref(0);
const isLoading = ref(false);
const pollingTimer = ref<number | null>(null);

interface MerchantInfo {
  name: string;
  cpf: string;
  coingate: string;
  pix: string;
}

interface UserInfo {
  paymentMethod: string;
  name: string;
  cpf: string;
  coingate: string;
}

// 商户信息
const merchantInfo = ref<MerchantInfo>({
  name: "",
  cpf: "",
  coingate: "Box777",
  pix: "",
});

// 用户信息
const userInfo = ref<UserInfo>({
  paymentMethod: "cashpay",
  name: "",
  cpf: "",
  coingate: "",
});
const limit_min = ref();
const limit_max = ref();
// 加载商户信息
const merchantInfoIsEdit = ref(false);
const loadMerchantInfo = async () => {
  try {
    const response = await queryUserBinding();
    if (response) {
      merchantInfo.value = {
        name: response.customer_name || "",
        cpf: response.cpf || "",
        // coingate: response.merchant_coingate || "Box777",
      };
      merchantInfoIsEdit.value =
        response.cpf.length > 0 ? true : false;
      // 更新用户信息
      userInfo.value = {
        ...userInfo.value,
        name: response.customer_name || "",
        cpf: response.cpf || "",
        // coingate: response.coingate || "",
      };
    }
  } catch (error) {
    console.error("Failed to load merchant info:", error);
    showError("Falha ao carregar informações do comerciante");
  }
};
const payOptions = ref();
// 获取系统支付通道列表
const getPaychannelsOpts = () => {
  getPaychannels().then((res) => {
    payOptions.value = res
      .filter((e) => e.status)
      .map((e) => ({
        title: e.channel_name,
        value: e.channel_type,
        limit_min: e.min_recharge_amount,
        limit_max: e.max_recharge_amount,
      }));

    limit_min.value = payOptions.value.filter(
      (i) => i.value === userInfo.value.paymentMethod
    )[0]?.limit_min;
    limit_max.value = payOptions.value.filter(
      (i) => i.value === userInfo.value.paymentMethod
    )[0]?.limit_max;
  });
};

const rechargeLists = ref();
// 获取系统充值礼包列表
const getRechargeListsCard = () => {
  getRechargeLists().then((res) => {
    rechargeLists.value = res;
  });
};

// 选中充值礼包,赋值充值金额
const selection = ref();
// 监听选中礼包变化
watchEffect(() => {
  if (selection.value) {
    const obj = rechargeLists.value.filter(
      (e: { id: any }) => e.id === selection.value
    )[0];
    selectedAmount.value = obj.recharge_amount / 100;
    extraAmount.value = obj.cash_amount
      ? obj.cash_amount / 100
      : obj.bonus_amount
      ? obj.bonus_amount / 100
      : obj.bet_cash_amount / 100;
  } else {
    selectedAmount.value = "";
    extraAmount.value = "";
  }
});

// 充值金额类型切换
const handleUpdate = (e) => {
  console.log(e);
  selectedAmount.value = "";
  selection.value = null;

  limit_min.value = payOptions.value.filter((i) => i.value === e)[0]?.limit_min;
  limit_max.value = payOptions.value.filter((i) => i.value === e)[0]?.limit_max;
};

const nameError = ref("");
const cpfError = ref("");
const amountError = ref("");

const validateRequiredFields = () => {
  let valid = true;
  nameError.value = '';
  cpfError.value = '';
  amountError.value = '';
  if (userInfo.value.paymentMethod === 'cashpay') {
    if (!merchantInfo.value.name) {
      nameError.value = 'Nome é obrigatório';
      valid = false;
    }
    if (!merchantInfo.value.cpf) {
      cpfError.value = 'CPF é obrigatório';
      valid = false;
    }
  }
  if (!selectedAmount.value) {
    amountError.value = 'Quantidade necessária';
    valid = false;
  }
  return valid;
};

onMounted(() => {
  loadMerchantInfo();
  getPaychannelsOpts();
  getRechargeListsCard();
});

// 生成交易号
const generateTransactionNo = () => {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `${timestamp}${random}`;
};

// 验证金额
const validateAmount = (amount: string) => {
  const numAmount = Number(amount);
  if (!amount || isNaN(numAmount)) {
    showError("Por favor, insira um valor válido");
    return false;
  }
  if (numAmount < limit_min.value) {
    showError("O valor mínimo de depósito é R$" + limit_min.value);
    return false;
  }
  return true;
};

// 开始轮询充值状态
const startPolling = (orderId: string) => {
  // 清除可能存在的旧定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }

  let attempts = 0;
  const maxAttempts = 60; // 最多轮询60次，即5分钟

  pollingTimer.value = window.setInterval(async () => {
    try {
      attempts++;
      const response = await queryDepositStatus(orderId);
      let type = userInfo.value.paymentMethod === "coingate";
      if (
        (type && response.status === "completed") ||
        response.status === "01"
      ) {
        // 充值成功
        clearInterval(pollingTimer.value!);
        await store.dispatch("auth/fetchUserInfo"); // 更新用户信息
        showSuccess("Depósito realizado com sucesso!");
      } else if (
        (type && response.status === "failed") ||
        response.status === "02"
      ) {
        // 充值失败
        clearInterval(pollingTimer.value!);
        showError("Falha ao processar o depósito");
      } else if (attempts >= maxAttempts) {
        // 超过最大尝试次数
        clearInterval(pollingTimer.value!);
      }
    } catch (error) {
      console.error("Failed to query deposit status:", error);
    }
  }, 5000); // 每5秒查询一次
};

// 提交充值
const handleSubmit = async () => {
  try {
    if (!validateRequiredFields()) {
      return;
    }
    // 验证金额
    if (!validateAmount(selectedAmount.value)) {
      return;
    }

    isLoading.value = true;

    const params: DepositParams = {
      user_id: store.state.auth.user.id,
      amount: Number(selectedAmount.value) * 100,
      currency: userInfo.value.paymentMethod === "coingate" ? "USDT" : "BRL",
      payment_method: userInfo.value.paymentMethod,
      transaction_no: generateTransactionNo(),
      cpf:merchantInfo.value.cpf,
      customer_name:merchantInfo.value.name
    };
    // 判断是否选中礼包，选中则赋值礼包id
    if (selection.value) {
      params.package_id = selection.value;
    }
    const response = await submitDeposit(params);

    // 打开支付页面 - 改进的移动端兼容性处理
    const paymentUrl =
      userInfo.value.paymentMethod === "coingate"
        ? response.payment_url
        : response.pay_url;
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      // iOS需要用户交互触发window.open
      setTimeout(() => {
        window.location.href = paymentUrl;
      }, 100);
    } else {
      window.open(paymentUrl, "_self");
    }

    // 开始轮询充值状态
    startPolling(response.order_id);
  } catch (error: any) {
    showError(error?.message || "Falha ao processar o depósito");
  } finally {
    isLoading.value = false;
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }
});
</script>

<style lang="scss" scoped>
.deposit-container {
  min-height: 100%;
  padding: 16px;
}

.deposit-form {
  background: #30a965;
  border-radius: 14px;
  margin-top: 8px;
  max-height: 520px;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
  ::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  :deep(.v-card) {
    padding: 0;
  }
  :deep(.v-messages){
    font-weight: 800;
    font-size: 16px;
    margin: 3px 0;
    // color: red !important;
  }
}
.sorteio {
  width: max-content;
  color: #ffdf00;
}
.amount-input {
  position: relative;
  :deep() {
    .v-field__prepend-inner {
      color: #ffdf00;
      font-weight: 400;
      font-size: 17px;
    }
    .v-input__control {
      border-radius: 8px !important;
      height: 44px !important;
      background: #002664;
    }
    .v-field__overlay {
      border-radius: 8px !important;
    }
    .v-field-label {
      color: #cfd3dc;
    }
    .v-input__details {
      line-height: 8px;
      min-height: 8px;
      padding-top: 0;
    }
    .v-field__outline {
      border-radius: 8px !important;
      border-color: #002664 !important;
      .v-field__outline__start,
      .v-field__outline__notch,
      .v-field__outline__end {
        border-color: #002664 !important;
        &::before {
          border-color: #002664 !important;
        }
        &::after {
          border-color: #002664 !important;
        }
      }
    }
    .v-field__field {
      height: 44px;
      line-height: 44px;
    }
    .v-field__input {
      padding: 0 16px !important;
      min-height: 44px;
      height: 30px;
    }
  }
}

.input-extra {
  position: absolute;
  right: 16px;
  top: 12px;
  color: #ffdf00;
  font-size: 14px;
}

.payment-section {
  margin-bottom: 24px;
  .withdraw-card {
    background: #343f6b;
    border-radius: 8px;
    color: #cfd3dc;
    font-size: 17px;
    img {
      width: 22px;
    }
  }
}

.submit-btn {
  height: 40px;
  background: linear-gradient(0deg, #c9b737, #2abb27);
  border-radius: 20px;
}
.recharge-package {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 8px;
  padding: 0 12px 12px 12px;
  position: relative;
  z-index: 1;
  // background-color: #002664;
  .recharge-package-price {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .bonus-tag {
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    background: linear-gradient(90deg, #ff69b4, #ff1493);
    padding: 4px 12px;
    border-radius: 20px;
    margin-bottom: 4px;
    box-shadow: 0 2px 8px rgba(255, 20, 147, 0.3);
  }

  .bonus-amount {
    font-size: 28px;
    font-weight: 800;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 8px;
  }

  // .bonus-chances {
  //   position: absolute;
  //   bottom: 0;
  //   left: 0;
  //   width: 100%;
  //   font-size: 15px;
  //   height: 26px;
  //   line-height: 26px;
  //   color: #fff;
  //   background: linear-gradient(87deg, #251d31, #32951f, #251d31);
  //   z-index: 2;
  // }
}
:deep(.v-col-6) {
  padding: 5px !important;
}
.v-card {
  background: #002664;
  // background: linear-gradient(180deg, #1a2347, #0e1536) !important;
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px !important;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 160px !important; /* Fixed height based on image */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Align items to the top to place bonus tag */
  // padding-top: 16px;

  &::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.3), transparent);
    z-index: 0;
  }

  &:hover {
    // transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  }

  &.bg-select {
    background: linear-gradient(
      180deg,
      #1a2347,
      #0e1536
    ) !important; /* Keep background but change border */
    border: 1px solid #ffdf00; // Change border color to golden
    box-shadow: inset 0 0 15px 5px rgba(255, 223, 0, 0.6); // Adjust box-shadow for golden glow

    &::before {
      background: linear-gradient(0deg, rgba(255, 223, 0, 0.1), transparent);
    }
  }
}
@media (max-width: 768px) {
  .deposit-container {
    padding: 0 12px 12px 12px;
  }
  .v-card {
    height: 90px !important;
  }
  .recharge-package {
    padding-bottom: 0;
    gap: 0;
    .bonus-amount {
      margin: 0;
      font-size: 20px;
    }
  }
  // .bonus-item {
  //   .bonus-amount {
  //     font-size: 12px;
  //   }
  // }
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.withdrawal-hint {
  background-color: #f48fb1; /* 類似圖片的粉紅色 */
  border-radius: 14px; /* 圓角 */
  padding: 12px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  color: white;
  font-size: 14px;
}

.hint-text {
  flex-grow: 1;
  margin-right: 12px;
}
</style>
