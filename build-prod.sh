#!/bin/bash
REMOTE_USER="ubuntu"
REMOTE_HOST1="box-777-server01"
REMOTE_HOST2="box-777-server02"
COMPOSE_FILE="/data/admin/docker-compose.yml"
SERVICE_NAME="box777-admin"  # 要更新的服务名

docker build --no-cache -t lkeke/box777-admin:v1.0 .
echo "推送镜像"
docker push lkeke/box777-admin:v1.0
# 远程执行命令
echo "推送成功"
  


ssh $REMOTE_USER@$REMOTE_HOST1 "docker-compose -f ${COMPOSE_FILE} down"
#ssh $REMOTE_USER@$REMOTE_HOST1 "bash /opt/docker/app/remove_images.sh" 

ssh $REMOTE_USER@$REMOTE_HOST1 "docker-compose -f ${COMPOSE_FILE} pull "
ssh $REMOTE_USER@$REMOTE_HOST1 "docker-compose -f ${COMPOSE_FILE} up -d  "

ssh $REMOTE_USER@$REMOTE_HOST2 "docker-compose -f ${COMPOSE_FILE} down"
#ssh $REMOTE_USER@$REMOTE_HOST2 "bash /opt/docker/app/remove_images.sh"
ssh $REMOTE_USER@$REMOTE_HOST2 "docker-compose -f ${COMPOSE_FILE} pull "
ssh $REMOTE_USER@$REMOTE_HOST2 "docker-compose -f ${COMPOSE_FILE} up -d "
