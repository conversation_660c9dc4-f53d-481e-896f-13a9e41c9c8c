<script setup lang="tsx">
import { ref } from "vue";
import { ElButton, ElTag, ElMessage } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import {
  fetchJackpotList,
  fetchJackpotConfigStatus,
} from "@/service/api/jackpot";
import JackpotOperateDrawer from "./modules/jackpot-operate-drawer.vue";
import JackpotOperateSearch from "./modules/jackpot-operate-search.vue";
import JackpotRankDrawer from "./modules/jackpot-rank-drawer.vue";
import JackpotProfitDialog from "./modules/jackpot-profit-dialog.vue";
import JackpotStatusDialog from "./modules/jackpot-status-dialog.vue";
import moment from "moment";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

const rankDrawerVisible = ref(false);
const rankDrawerJackpotId = ref(0);
//平台利润注入弹窗
const profitDialogVisible = ref(false);
const profitDialogJackpotId = ref(0);
//状态改变弹窗
const statusDialogVisible = ref(false);
const statusDialogType = ref("online");
const statusDialogJackpotId = ref(0);
const statusDialogJackpotName = ref("");

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchJackpotList,
  showTotal: true,
  apiParams: { page: 1, size: 20, status: undefined },
  columns: () => [
    { prop: "id", label: "ID", width: 64 },
    { prop: "jackpot_name", label: "奖池名称", minWidth: 150 },
    {
      prop: "start_time",
      label: "开始时间",
      minWidth: 120,
      formatter: (row) =>
        row.start_time ? moment(row.start_time).format("YYYY-MM-DD") : "-",
    },
    {
      prop: "end_time",
      label: "结束时间",
      minWidth: 120,
      formatter: (row) =>
        row.end_time ? moment(row.end_time).format("YYYY-MM-DD") : "-",
    },
    {
      prop: "fixed_bonus",
      label: "固定奖金",
      minWidth: 120,
      formatter: (row) => `R$${row.fixed_bonus / 100}`,
    },
    {
      prop: "initial_seed_amount",
      label: "初始种子金额",
      minWidth: 120,
      formatter: (row) => `R$${row.initial_seed_amount / 100}`,
    },
    {
      prop: "platform_profit_percentage",
      label: "平台利润百分比",
      minWidth: 120,
      formatter: (row) => `${row.platform_profit_percentage}%`,
    },
    {
      prop: "",
      label: "平台利润注入",
      minWidth: 120,
      formatter: (row) => (
        <span
          style="color:#656cf0;cursor:pointer"
          onClick={() => handlePlatformProfit(row)}
        >
          R${row.platform_profit_injection}
        </span>
      ),
    },
    {
      prop: "jackpot_initial_status",
      label: "状态",
      minWidth: 100,
      formatter: (row) => {
        let label = "即将开启",
          type = "info";
        if (row.jackpot_initial_status === 1) {
          label = "进行中";
          type = "success";
        } else if (row.jackpot_initial_status === 2) {
          label = "已结束";
          type = "danger";
        }
        return <ElTag type={type}>{label}</ElTag>;
      },
    },
    {
      prop: "operate",
      label: "操作",
      width: 180,
      align: "center",
      formatter: (row) => (
        <div class="flex-center">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => handleEdit(row.id)}
            >
              编辑
            </ElButton>
          )}
          {hasAuth(2) &&
            (row.jackpot_initial_status === 0 ||
              row.jackpot_initial_status === 2) && (
              <ElButton
                type="success"
                plain
                size="small"
                onClick={() => handleStart(row)}
              >
                开始
              </ElButton>
            )}
          {hasAuth(2) && row.jackpot_initial_status === 1 && (
            <ElButton
              type="danger"
              plain
              size="small"
              onClick={() => handleEnd(row)}
            >
              结束
            </ElButton>
          )}
          {hasAuth(4) && (
            <ElButton
              type="success"
              plain
              size="small"
              onClick={() => handleRank(row.id)}
            >
              排名
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit } =
  useTableOperate(data, getData, "id");

async function upadtefetchJackpotConfigStatus() {
  const { error } = await fetchJackpotConfigStatus({
    id: statusDialogJackpotId.value,
    status: statusDialogType.value === "online" ? 1 : 2,
  });
  if (!error) {
    window.$message?.success("更新成功");
    getDataByPage();
    statusDialogVisible.value = false;
  }
}

function handlePlatformProfit(row) {
  if (row.jackpot_initial_status === 0) {
    ElMessage.error("奖池未开启");
    return;
  }
  profitDialogJackpotId.value = row.id;
  profitDialogVisible.value = true;
}

function handleRank(id) {
  rankDrawerJackpotId.value = id;
  rankDrawerVisible.value = true;
}

function handleStart(row) {
  statusDialogJackpotId.value = row.id;
  statusDialogJackpotName.value = row.jackpot_name;
  statusDialogType.value = "online";
  statusDialogVisible.value = true;
}

function handleEnd(row) {
  statusDialogJackpotId.value = row.id;
  statusDialogJackpotName.value = row.jackpot_name;
  statusDialogType.value = "offline";
  statusDialogVisible.value = true;
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <JackpotOperateSearch
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
          getDataByPage();
        }
      "
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :isNoDelete="true"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </JackpotOperateSearch>

    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-55px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <JackpotOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
      <JackpotRankDrawer
        v-model:visible="rankDrawerVisible"
        :jackpot-id="rankDrawerJackpotId"
      />
      <JackpotProfitDialog
        v-model:visible="profitDialogVisible"
        :jackpot-id="profitDialogJackpotId"
      />
      <JackpotStatusDialog
        v-model:visible="statusDialogVisible"
        :type="statusDialogType"
        :jackpot-id="statusDialogJackpotId"
        :jackpot-name="statusDialogJackpotName"
        @confirm="upadtefetchJackpotConfigStatus"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
