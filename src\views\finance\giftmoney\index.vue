<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed } from "vue";
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElPagination,
  ElTag,
  ElDialog,
  ElRadioGroup,
  ElRadio,
  ElRow,
  ElCol,
  ElMessage,
} from "element-plus";
import { useTable } from "@/hooks/common/table";
import {
  getDigitalBonusList,
  manualAllocate,
  manualReduction,
  userInfo,
  type DigitalBonusRecord,
} from "@/service/api/digitalBonus";
import type { TableColumnCtx } from "element-plus/es/components/table/src/table-column/defaults";
import { formatNumber } from "@/utils/format";

// 搜索参数类型
// interface SearchParams {
//   user_id?: string;
//   source_type?: string;
//   status?: string;
//   start_time?: string;
//   end_time?: string;
//   bonus_type?: string;
//   page?: number;
//   size?: number;
// }

// 活动类型选项
const activityTypeOptions = [
  { label: "全部", value: "" },
  { label: "邀请赠送", value: "invitation" },
  { label: "注册奖励", value: "registration" },
  { label: "首充奖励", value: "first_deposit" },
  { label: "充值奖励", value: "recharge" },
  { label: "奖池奖励", value: "jackpot" },
  { label: "拼多多任务奖励", value: "pdd_task" },
  { label: "拼多多助力奖励", value: "pdd_assist" },
  { label: "手动派发", value: "manual_allocation" },
  { label: "手动扣减", value: "manual_reduction" },
];

// 赠金类型选项
const giftTypeOptions = [
  { label: "全部", value: "" },
  { label: "赠金", value: "bonus" },
  { label: "现金", value: "cash" },
  { label: "打码提现", value: "bet_cash" },
];

// 自定义表格列类型
interface MyTableColumn extends Partial<TableColumnCtx<any>> {
  slot?: boolean;
}

// useTable 配置
const {
  columns: tableColumns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable<any>({
  apiFn: getDigitalBonusList,
  apiParams: {
    page: 1,
    size: 10,
    user_id: undefined,
    source_type: undefined,
    bonus_type: undefined,
    start_time: undefined,
    end_time: undefined,
  },
  columns: () =>
    [
      { prop: "index", label: "序号", width: 64 },
      { prop: "user_id", label: "用户ID", minWidth: 100 },
      { prop: "user_name", label: "用户名", minWidth: 120 },
      {
        prop: "amount",
        label: "赠金金额",
        minWidth: 100,
        formatter: (row) => `R$ ${formatNumber(row.amount / 100)}`,
      },
      { prop: "source_type_name", label: "来源类型", minWidth: 120 },
      { prop: "bonus_type_name", label: "赠送类型", minWidth: 100 },
      { prop: "op_type_name", label: "操作类型", width: 80 },
      {
        prop: "status_name",
        label: "状态",
        width: 100,
        slot: true,
      },
      { prop: "expiry_time_str", label: "过期时间", minWidth: 160 },
      { prop: "created_at_str", label: "创建时间", minWidth: 160 },
      {
        prop: "remark",
        label: "备注",
        minWidth: 200,
        showOverflowTooltip: true,
      },
    ] as MyTableColumn[],
  immediate: true,
});

// 搜索表单双向绑定辅助
const dateRange = ref<[string, string] | undefined>();

// 搜索
function handleSearch() {
  // 拆分日期
  if (dateRange.value) {
    searchParams.start_time = new Date(dateRange.value[0]).getTime().toString();
    searchParams.end_time = new Date(dateRange.value[1]).getTime().toString();
  } else {
    searchParams.start_time = undefined;
    searchParams.end_time = undefined;
  }
  getDataByPage();
}

// 重置
async function handleReset() {
  dateRange.value = undefined;
  await nextTick();
  resetSearchParams();
  handleSearch();
}

// 状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, "success" | "info" | "danger"> = {
    valid: "success",
    invalid: "info",
    expired: "danger",
  };
  return typeMap[status] || "info";
};

// 手动派发弹窗相关
const grantDialogVisible = ref(false);
const grantForm = reactive({
  user_ids: [] as (string | number)[],
  amount: undefined as number | undefined,
  bonus_type: "bonus",
  remark: "",
});
const grantUserIdInput = ref("");

// 用戶遠端搜索選項與加載狀態
const userOptions = ref<any[]>([]);
const userLoading = ref(false);

// 遠端搜索方法
async function remoteUserSearch(query: string) {
  if (!query) {
    // userOptions.value = [];
    return;
  }
  userLoading.value = true;
  try {
    // 假設 userInfo 支持根據 user_id 查詢
    const res = await userInfo({ user_id: query });
    if (
      res &&
      res.data &&
      res.data.data &&
      Object.keys(res.data.data).length > 0
    ) {
      // 先合并新老数据，再根据 value 字段去重
      const newOption = {
        value: res.data.data.user_id,
        label: res.data.data.user_id,
      };
      // 合并后去重
      const merged = [...userOptions.value, newOption];
      // 用 Map 根据 value 去重
      userOptions.value = Array.from(
        new Map(merged.map((item) => [item.value, item])).values(),
      );
    } else {
      userOptions.value = [];
    }
  } catch {
    userOptions.value = [];
  } finally {
    userLoading.value = false;
  }
}
const deductFormRef = ref();
const grantFormRef = ref();
// 表單驗證規則
const grantFormRules = {
  user_ids: [
    { required: true, message: "请选择用户", trigger: "change" },
    {
      validator: (
        rule: any,
        value: (string | number)[],
        callback: Function,
      ) => {
        if (value.length > 10) {
          callback(new Error("最多只能选择10个用户"));
          return;
        }
        callback();
      },
      trigger: "change",
    },
  ],
  amount: [
    { required: true, message: "请输入派发金额", trigger: "blur" },
    {
      validator: (rule: any, value: number | string, callback: Function) => {
        if (value === undefined || value === null || value === "") {
          callback(new Error("请输入派发金额"));
          return;
        }
        const num = Number(value);
        if (isNaN(num) || num <= 0) {
          callback(new Error("派发金额必须为大于0的数字"));
          return;
        }
        if (!/^\d+(\.\d{1,2})?$/.test(String(value))) {
          callback(new Error("最多只能有两位小数"));
          return;
        }
        callback();
      },
      trigger: "blur",
    },
  ],
  bonus_type: [
    { required: true, message: "请选择赠金类型", trigger: "change" },
  ],
};

// 赠金类型选项
const bonusTypeOptions = [
  { label: "赠金", value: "bonus" },
  { label: "现金", value: "cash" },
  { label: "打码提现", value: "bet_cash" },
];

// 提交表单
async function handleGrantSubmit() {
  await grantFormRef.value.validate();
  grantLoading.value = true;
  try {
    const res = await manualAllocate({
      user_ids: grantForm.user_ids.map((id) => Number(id)), // 提交時轉為 number[]
      amount: grantForm.amount! * 100, // 转换为分
      bonus_type: grantForm.bonus_type,
      remark: grantForm.remark,
    });
    if (res?.data) {
      ElMessage.success("派发成功");
      grantDialogVisible.value = false;
      getData(); // 刷新列表
    }
  } catch (error) {
    console.error("派发失败:", error);
    ElMessage.error("派发失败");
  } finally {
    grantLoading.value = false;
  }
}

// 手动扣减弹窗相关
const deductDialogVisible = ref(false);
const deductForm = reactive({
  user_id: "" as string,
  cash_amount: undefined as number | undefined,
  bonus_amount: undefined as number | undefined,
  bet_cash_amount: undefined as number | undefined, // 新增
  remark: "",
});

// 新增：用於存放查詢到的用戶信息
const deductUserInfo = ref<any>(null);
const deductUserInfoLoading = ref(false);
const deductUserInfoError = ref("");
const maxCashBalance = computed(() => {
  return deductUserInfo.value?.cash_balance
    ? deductUserInfo.value.cash_balance / 100
    : 0;
});
const maxBonusBalance = computed(() => {
  return deductUserInfo.value?.bonus_balance
    ? deductUserInfo.value.bonus_balance / 100
    : 0;
});
// const maxBetCashBalance = computed(() => {
//   return deductUserInfo.value?.bet_cash_balance
//     ? deductUserInfo.value.bet_cash_balance / 100
//     : 0;
// });

// 新增：手动触发查询方法
async function handleDeductUserQuery() {
  deductUserInfo.value = null;
  deductUserInfoError.value = "";
  const val = deductForm.user_id;
  if (val && /^\d+$/.test(val)) {
    deductUserInfoLoading.value = true;
    try {
      const res = await userInfo({ user_id: val });
      if (res && res.data) {
        deductUserInfo.value = res.data.data;
      } else {
        deductUserInfo.value = null;
        deductUserInfoError.value = "未查到用户信息";
      }
    } catch (e) {
      deductUserInfo.value = null;
      deductUserInfoError.value = "查询失败";
    } finally {
      deductUserInfoLoading.value = false;
    }
  }
}

// 表單驗證規則
const deductFormRules = {
  user_id: [
    { required: true, message: "请输入用户ID", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (!/^\d+$/.test(value)) {
          callback(new Error("用户ID必须为数字"));
          return;
        }
        callback();
      },
      trigger: "blur",
    },
  ],
  cash_amount: [
    {
      validator: (rule: any, value: number | undefined, callback: Function) => {
        if (!deductUserInfo.value) {
          callback(new Error("请先搜索用户信息"));
          return;
        }
        if (value === undefined || value === null) {
          callback();
          return;
        }
        const num = Number(value);
        if (isNaN(num) || num <= 0) {
          callback(new Error("扣减金额必须为大于0的正整数"));
          return;
        }
        if (!Number.isInteger(num)) {
          callback(new Error("扣减金额必须为正整数"));
          return;
        }
        if (num > maxCashBalance.value) {
          callback(new Error("扣减金额必须小于等于钱包现金金额" + maxCashBalance.value));
          return;
        }
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  bonus_amount: [
    {
      validator: (rule: any, value: number | undefined, callback: Function) => {
        if (!deductUserInfo.value) {
          callback(new Error("请先搜索用户信息"));
          return;
        }
        if (value === undefined || value === null) {
          callback();
          return;
        }
        const num = Number(value);
        if (isNaN(num) || num <= 0) {
          callback(new Error("扣减金额必须为大于0的正整数"));
          return;
        }
        if (!Number.isInteger(num)) {
          callback(new Error("扣减金额必须为正整数"));
          return;
        }
        if (num > maxBonusBalance.value) {
          callback(new Error("扣减金额必须小于等于钱包赠金金额" + maxBonusBalance.value));
          return;
        }
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  // bet_cash_amount: [
  //   {
  //     validator: (rule: any, value: number | undefined, callback: Function) => {
  //       if (!deductUserInfo.value) {
  //         callback(new Error("请先搜索用户信息"));
  //         return;
  //       }
  //       if (value === undefined || value === null) {
  //         callback();
  //         return;
  //       }
  //       const num = Number(value);
  //       if (isNaN(num) || num <= 0) {
  //         callback(new Error("扣减金额必须为大于0的正整数"));
  //         return;
  //       }
  //       if (!Number.isInteger(num)) {
  //         callback(new Error("扣减金额必须为正整数"));
  //         return;
  //       }
  //       if (num > maxBetCashBalance.value) {
  //         callback(new Error("扣减金额必须小于等于钱包打码金额" + maxBetCashBalance.value));
  //         return;
  //       }
  //       callback();
  //     },
  //     trigger: ["blur", "change"],
  //   },
  // ],
};

// 提交扣减表单
async function handleDeductSubmit() {
  await deductFormRef.value.validate();
  deductLoading.value = true;
  try {
    // 检查是否至少输入了一种扣减金额
    if (!deductForm.cash_amount && !deductForm.bonus_amount) {
      ElMessage.warning("请至少输入一种扣减金额");
      deductLoading.value = false;
      return;
    }

    const res = await manualReduction({
      user_id: parseInt(deductForm.user_id),
      cash_amount: deductForm.cash_amount ? deductForm.cash_amount * 100 : 0, // 转换为分
      bonus_amount: deductForm.bonus_amount ? deductForm.bonus_amount * 100 : 0, // 转换为分
      // bet_cash_amount: deductForm.bet_cash_amount ? deductForm.bet_cash_amount * 100 : 0, // 新增
      remark: deductForm.remark,
    });
    if (res?.data) {
      ElMessage.success("扣减成功");
      deductDialogVisible.value = false;
      getData(); // 刷新列表
    }
  } catch (error) {
    console.error("扣减失败:", error);
    ElMessage.error("扣减失败");
  } finally {
    deductLoading.value = false;
  }
}

// 打开手动派发弹窗
function handleManualGrant() {
  grantDialogVisible.value = true;
  // 重置表单内容
  Object.assign(grantForm, {
    user_ids: [],
    amount: undefined,
    bonus_type: "bonus",
    remark: "",
  });
}

// 打开手动扣减弹窗
function handleManualDeduct() {
  deductDialogVisible.value = true;
  deductUserInfo.value = null;
  // 重置表单
  Object.assign(deductForm, {
    user_id: "",
    cash_amount: undefined,
    bonus_amount: undefined,
    bet_cash_amount: undefined, // 新增
    remark: "",
  });
}

// 在<script setup>中添加：
const grantUserSelectRef = ref();

// 新增 loading 状态
const grantLoading = ref(false);
const deductLoading = ref(false);

function onAmountInput(val: string | number) {
  let str = String(val);
  // 只保留数字和小数点
  str = str.replace(/[^\d.]/g, "");
  // 只允许出现一个小数点
  str = str.replace(/^(\d*)(\.(\d{0,2})?).*$/, "$1$2");
  // 去除多余的前导0
  if (str.indexOf(".") === -1) {
    str = str.replace(/^0+/, "") || "0";
  }
  grantForm.amount = str;
}

// 现金金额输入限制 - 只允许正整数
function onCashAmountInput(val: string | number) {
  let str = String(val);
  // 只保留数字
  str = str.replace(/[^\d]/g, "");
  // 去除前导0，但保留单个0
  str = str.replace(/^0+/, "") || "0";
  // 如果为空，设置为undefined
  if (str === "0" || str === "") {
    deductForm.cash_amount = undefined;
  } else {
    deductForm.cash_amount = parseInt(str);
  }
}

// 赠金金额输入限制 - 只允许正整数
function onBonusAmountInput(val: string | number) {
  let str = String(val);
  // 只保留数字
  str = str.replace(/[^\d]/g, "");
  // 去除前导0，但保留单个0
  str = str.replace(/^0+/, "") || "0";
  // 如果为空，设置为undefined
  if (str === "0" || str === "") {
    deductForm.bonus_amount = undefined;
  } else {
    deductForm.bonus_amount = parseInt(str);
  }
}

function onBetCashAmountInput(val: string | number) {
  let str = String(val);
  str = str.replace(/[^\d]/g, "");
  str = str.replace(/^0+/, "") || "0";
  if (str === "0" || str === "") {
    deductForm.bet_cash_amount = undefined;
  } else {
    deductForm.bet_cash_amount = parseInt(str);
  }
}

defineOptions({ name: "GiftMoneyRecord" });
</script>

<template>
  <div
    class="min-h-500px p-6 flex-grow flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <div class="search-wrapper">
      <ElForm :model="searchParams" inline>
        <ElRow :gutter="16">
          <ElCol :span="4">
            <ElFormItem label="用户ID">
              <ElInput
                v-model="searchParams.user_id"
                placeholder="请输入用户ID"
                clearable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="4">
            <ElFormItem label="活动类型">
              <ElSelect
                v-model="searchParams.source_type"
                placeholder="所有类型"
                clearable
              >
                <ElOption
                  v-for="option in activityTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="4">
            <ElFormItem label="赠送类型">
              <ElSelect
                v-model="searchParams.bonus_type"
                placeholder="全部"
                clearable
              >
                <ElOption
                  v-for="option in giftTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="5">
            <ElFormItem label="日期">
              <ElDatePicker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="7">
            <ElFormItem>
              <ElButton type="primary" @click="handleSearch">搜索</ElButton>
              <ElButton @click="handleReset">重置</ElButton>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>

    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-120px)]">
        <div class="mb-16px flex justify-end gap-2">
          <ElButton type="primary" @click="handleManualGrant"
            >手动派发</ElButton
          >
          <ElButton type="danger" @click="handleManualDeduct"
            >手动扣减</ElButton
          >
        </div>
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn
            v-for="col in tableColumns"
            :key="col.prop || col.type"
            v-bind="col"
          >
            <template v-if="col.slot" #default="{ row }">
              <ElTag :type="getStatusType(row.status)">{{
                row.status_name
              }}</ElTag>
            </template>
          </ElTableColumn>
        </ElTable>
        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>

  <!-- 手动派发弹窗 -->
  <ElDialog
    v-model="grantDialogVisible"
    title="手动派发"
    width="500px"
    destroy-on-close
  >
    <ElForm
      :model="grantForm"
      :rules="grantFormRules"
      ref="grantFormRef"
      label-width="80px"
    >
      <ElFormItem label="赠金类型" prop="bonus_type">
        <ElRadioGroup v-model="grantForm.bonus_type">
          <ElRadio
            v-for="option in bonusTypeOptions"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="选择对象" prop="user_ids">
        <ElSelect
          ref="grantUserSelectRef"
          v-model="grantForm.user_ids"
          multiple
          filterable
          remote
          :remote-method="remoteUserSearch"
          :loading="userLoading"
          placeholder="请输入用户ID搜索"
          :collapse-tags="false"
          :max-collapse-tags="3"
          :multiple-limit="10"
          style="flex: 1"
        >
          <ElOption
            v-for="item in userOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="派发金额" prop="amount">
        <ElInput
          v-model="grantForm.amount"
          placeholder="请输入金额"
          type="number"
          min="0.01"
          step="0.01"
          @input="onAmountInput"
        />
      </ElFormItem>
      <ElFormItem label="备注">
        <ElInput
          v-model="grantForm.remark"
          placeholder="请输入"
          type="textarea"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="grantDialogVisible = false">取消</ElButton>
      <ElButton
        type="primary"
        :loading="grantLoading"
        @click="handleGrantSubmit"
        >确认</ElButton
      >
    </template>
  </ElDialog>

  <!-- 手动扣减弹窗 -->
  <ElDialog
    v-model="deductDialogVisible"
    title="手动扣减"
    width="500px"
    destroy-on-close
  >
    <ElForm
      :model="deductForm"
      :rules="deductFormRules"
      ref="deductFormRef"
      label-width="100px"
    >
      <ElFormItem label="用户ID" prop="user_id">
        <el-row gutter="8" style="width: 100%">
          <el-col :span="18">
            <ElInput
              v-model="deductForm.user_id"
              @input="
                (e) => {
                  if (!e) {
                    deductUserInfo = null;
                  }
                }
              "
              placeholder="请输入用户ID"
            />
          </el-col>
          <el-col :span="6">
            <ElButton
              type="primary"
              @click="handleDeductUserQuery"
              style="width: 100%"
              >搜索</ElButton
            >
          </el-col>
        </el-row>
      </ElFormItem>
      <!-- 用户信息展示区块（已改为简体字） -->
      <div
        v-if="deductForm.user_id && /^\d+$/.test(deductForm.user_id)"
        style="margin-bottom: 12px"
      >
        <el-card
          v-if="deductUserInfoLoading"
          shadow="never"
          style="background: #f9f9f9; border: none"
        >
          查询中...
        </el-card>
        <div v-else-if="deductUserInfo" class="ml-10">
          <div class="user-info-block" v-if="deductUserInfo.user_id">
            <div class="user-info-row">
              <span>用户账号：</span>
              <span>{{
                deductUserInfo.username || deductUserInfo.user_id
              }}</span>
            </div>
            <div class="user-info-row">
              <span>注册时间：</span>
              <span>
                {{
                  deductUserInfo.created_at
                    ? new Date(
                        Number(deductUserInfo.created_at),
                      ).toLocaleString()
                    : "-"
                }}
              </span>
            </div>
            <div class="user-info-row user-info-balance-title">
              <span style="width: 80px; text-align: center">账户余额（R$）</span>
              <span style="width: 80px; text-align: center">现金</span>
              <span style="width: 80px; text-align: center">赠金</span>
              <!-- <span style="width: 80px; text-align: center">打码</span> -->
               <!-- 新增 -->
            </div>
            <div class="user-info-row user-info-balance-value">
              <span style="width: 80px"></span>
              <span style="width: 80px; text-align: center">
                {{
                  deductUserInfo.cash_balance != null
                    ? (deductUserInfo.cash_balance / 100).toFixed(2)
                    : "-"
                }}
              </span>
              <span style="width: 80px; text-align: center">
                {{
                  deductUserInfo.bonus_balance != null
                    ? (deductUserInfo.bonus_balance / 100).toFixed(2)
                    : "-"
                }}
              </span>
              <!-- <span style="width: 80px; text-align: center">
                {{
                  deductUserInfo.bet_cash_balance != null
                    ? (deductUserInfo.bet_cash_balance / 100).toFixed(2)
                    : "-"
                }}
              </span> -->
            </div>
          </div>
          <div class="user-info-block" v-else>
            <div class="user-info-row">
              <span>搜索用户账号无效</span>
            </div>
          </div>
        </div>
        <el-card
          v-else-if="deductUserInfoError"
          shadow="never"
          style="background: #f9f9f9; border: none; color: #f56c6c"
        >
          {{ deductUserInfoError }}
        </el-card>
      </div>
      <ElFormItem label="扣减金额" required>
        <ElRow :gutter="20" style="width: 100%">
          <ElCol :span="8">
            <ElFormItem prop="cash_amount">
              <ElInput
                v-model="deductForm.cash_amount"
                placeholder="现金金额"
                :max="maxCashBalance"
                type="number"
                step="1"
                min="1"
                @input="onCashAmountInput"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem prop="bonus_amount">
              <ElInput
                v-model="deductForm.bonus_amount"
                placeholder="赠金金额"
                :max="maxBonusBalance"
                type="number"
                step="1"
                min="1"
                @input="onBonusAmountInput"
              />
            </ElFormItem>
          </ElCol>
          <!-- <ElCol :span="8">
            <ElFormItem prop="bet_cash_amount">
              <ElInput
                v-model="deductForm.bet_cash_amount"
                placeholder="打码金额"
                :max="maxBetCashBalance"
                type="number"
                step="1"
                min="1"
                @input="onBetCashAmountInput"
              />
            </ElFormItem>
          </ElCol> -->
        </ElRow>
      </ElFormItem>
      <ElFormItem label="备注">
        <ElInput
          v-model="deductForm.remark"
          placeholder="请输入"
          type="textarea"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="deductDialogVisible = false">取消</ElButton>
      <ElButton
        type="primary"
        :loading="deductLoading"
        @click="handleDeductSubmit"
        >确认</ElButton
      >
    </template>
  </ElDialog>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
  border: none;
  padding: 20px 24px 24px 24px;
}

.search-wrapper {
  background: #fff;
  border-radius: 10px;
  margin-bottom: 18px;
  padding: 18px 24px 6px 24px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 24px;
      margin-bottom: 12px;
      &:last-child {
        margin-right: 0;
      }
      .el-input,
      .el-select {
        width: 180px;
      }
      .el-date-editor {
        width: 260px;
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-primary {
  color: var(--el-color-primary);
}

.text-success {
  color: var(--el-color-success);
}

.mb-16px {
  margin-bottom: 16px;
}

.card-wrapper {
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

:deep(.el-table th) {
  font-weight: bold;
  background: #f7f8fa;
}

:deep(.el-table .el-table__cell) {
  font-size: 14px;
}

:deep(.el-tag) {
  border-radius: 8px;
  font-size: 13px;
  padding: 0 10px;
}

:deep(.el-pagination) {
  justify-content: center;
  margin: 20px 0 0 0;
}

@media (max-width: 900px) {
  .search-wrapper {
    padding: 10px 6px 2px 6px;
  }
  :deep(.el-form--inline) {
    flex-direction: column;
    .el-form-item {
      margin-right: 0 !important;
      width: 100%;
    }
  }
  .card-wrapper {
    padding: 0;
  }
}

.user-info-block {
  font-size: 14px;
  line-height: 2;
  margin-bottom: 8px;
}
.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}
.user-info-balance-title {
  font-weight: bold;
  margin-top: 8px;
}
.user-info-balance-value {
  margin-top: 0;
}
</style>
