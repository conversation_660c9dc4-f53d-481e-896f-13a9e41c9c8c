<template>
  <v-container class="ranking-container" max-width="940">
    <div class="ranking-card">
      <!-- 标题区域 -->
      <div class="ranking-header">
        <img src="@/assets/images/vip-banner.png" />
      </div>

      <!-- 渐变线 -->
      <div class="line mb-4"></div>
      <v-card class="vip-content mb-4 pa-4" variant="text">
        <div class="d-flex flex-column align-center ga-4">
          <div class="d-flex align-center justify-sm-center ga-3">
            <v-btn
              :color="vipValue === 1 ? '#FFDF00' : '#40465E'"
              class="rounded-lg"
              @click="vipValue = 1"
              >Nivel VIP 0-5</v-btn
            >
            <v-btn
              :color="vipValue === 2 ? '#FFDF00' : '#40465E'"
              class="rounded-lg"
              @click="vipValue = 2"
              >Nivel VIP 6-10</v-btn
            >
          </div>
          <!-- 动态渲染 VIP 等级 1-5 -->
          <div
            v-if="vipValue === 1"
            class="vip-levels-container d-flex flex-column align-center"
          >
            <v-card
              v-for="vip in vipLevels1to5"
              :key="vip.level"
              :style="{ background: vip.bgColor }"
              class="vip-level-card mb-4"
            >
              <div class="card-privilegio pa-4 d-flex align-center">
                <div class="vip-icon-placeholder">
                  <v-img
                    :width="100"
                    aspect-ratio="16/9"
                    cover
                    :src="vip.avtar"
                  ></v-img>
                </div>
                <div class="ms-4">
                  <div class="text-h6 font-weight-bold">
                    Privilegio de membro Recompensas de login diário
                  </div>
                  <!-- <div>Recompensas de login diário</div> -->
                  <div>
                    Limite saque diario: R${{ vip.max_withdrawal / 100 }}
                  </div>
                  <div>
                    Número de saques por dia: {{ vip.withdrawal_count }}
                  </div>
                </div>
              </div>
              <div class="card-condices pa-4" v-if="vip.level">
                <div class="text-h6 font-weight-bold">
                  Condices do Nivel VIP{{ vip.level }}
                </div>
                <div>
                  Depósitos totais nos últimos 30 dias: R${{
                    vip.monthly_charge / 100
                  }}
                </div>
                <div>
                  Pontos de apostas nos últimos 30 dias:
                  {{ vip.monthly_bet / 100 }}
                </div>
              </div>
            </v-card>
          </div>
          <!-- 静态图片显示 VIP 6-10 (暂时保留，待后续实现) -->
          <!-- 动态渲染 VIP 等级 1-5 -->
          <div
            v-if="vipValue === 2"
            class="vip-levels-container d-flex flex-column align-center"
          >
            <v-card
              v-for="vip in vipLevels6to10"
              :key="vip.level"
              :style="{ background: vip.bgColor }"
              class="vip-level-card mb-4"
            >
              <div class="card-privilegio pa-4 d-flex align-center">
                <div class="vip-icon-placeholder">
                  <v-img
                    :width="100"
                    aspect-ratio="16/9"
                    cover
                    :src="vip.avtar"
                  ></v-img>
                </div>
                <div class="ms-4">
                  <div class="text-h6 font-weight-bold">
                    Privilegio de membro Recompensas de login diário
                  </div>
                  <!-- <div>Recompensas de login diário</div> -->
                  <div>
                    Limite saque diario: R${{ vip.max_withdrawal / 100 }}
                  </div>
                  <div>
                    Número de saques por dia: {{ vip.withdrawal_count }}
                  </div>
                </div>
              </div>
              <div class="card-condices pa-4">
                <div class="text-h6 font-weight-bold">
                  Condices do Nivel VIP{{ vip.level }}
                </div>
                <div>
                  Depósitos totais nos últimos 60 dias:R${{
                    vip.monthly_charge / 100
                  }}
                </div>
                <div>
                  Pontos de apostas nos últimos 60 dias:{{
                    vip.monthly_bet / 100
                  }}
                </div>
              </div>
            </v-card>
          </div>
          <!-- <img v-if="vipValue === 2" src="@/assets/images/vip-6-10.png" style="width: auto;max-width: 100%"/> -->
        </div>
      </v-card>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { vipLevels, type levelsResponse } from "@/api/user";

// 動態引入背景圖片
const vipBgImages = {
  vip0: new URL("@/assets/images/vip/vip-bg0.png", import.meta.url).href,
  vip1: new URL("@/assets/images/vip/vip-bg1.png", import.meta.url).href,
  vip2: new URL("@/assets/images/vip/vip-bg2.png", import.meta.url).href,
  vip3: new URL("@/assets/images/vip/vip-bg3.png", import.meta.url).href,
  vip4: new URL("@/assets/images/vip/vip-bg4.png", import.meta.url).href,
  vip5: new URL("@/assets/images/vip/vip-bg5.png", import.meta.url).href,
  vip6: new URL("@/assets/images/vip/vip-bg6.png", import.meta.url).href,
  vip7: new URL("@/assets/images/vip/vip-bg7.png", import.meta.url).href,
  vip8: new URL("@/assets/images/vip/vip-bg8.png", import.meta.url).href,
  vip9: new URL("@/assets/images/vip/vip-bg9.png", import.meta.url).href,
  vip10: new URL("@/assets/images/vip/vip-bg10.png", import.meta.url).href,
  vip1avtar: new URL("@/assets/images/vip/vip-avtar1.png", import.meta.url)
    .href,
  vip0avtar: new URL("@/assets/images/vip/vip-avtar0.png", import.meta.url)
    .href,
  vip2avtar: new URL("@/assets/images/vip/vip-avtar2.png", import.meta.url)
    .href,
  vip3avtar: new URL("@/assets/images/vip/vip-avtar3.png", import.meta.url)
    .href,
  vip4avtar: new URL("@/assets/images/vip/vip-avtar4.png", import.meta.url)
    .href,
  vip5avtar: new URL("@/assets/images/vip/vip-avtar5.png", import.meta.url)
    .href,
  vip6avtar: new URL("@/assets/images/vip/vip-avtar6.png", import.meta.url)
    .href,
  vip7avtar: new URL("@/assets/images/vip/vip-avtar7.png", import.meta.url)
    .href,
  vip8avtar: new URL("@/assets/images/vip/vip-avtar8.png", import.meta.url)
    .href,
  vip9avtar: new URL("@/assets/images/vip/vip-avtar9.png", import.meta.url)
    .href,
  vip10avtar: new URL("@/assets/images/vip/vip-avtar10.png", import.meta.url)
    .href,
};

const vipValue = ref(1);

// VIP 等级 1-5 的数据
const vipLevels1to5Bg = [
  {
    level: 0,
    bgColor: `url(${vipBgImages.vip0})`,
    avtar: vipBgImages.vip0avtar,
  },
  {
    level: 1,
    bgColor: `url(${vipBgImages.vip1})`,
    avtar: vipBgImages.vip1avtar,
  },
  {
    level: 2,
    bgColor: `url(${vipBgImages.vip2})`,
    avtar: vipBgImages.vip2avtar,
  },
  {
    level: 3,
    bgColor: `url(${vipBgImages.vip3})`,
    avtar: vipBgImages.vip3avtar,
  },
  {
    level: 4,
    bgColor: `url(${vipBgImages.vip4})`,
    avtar: vipBgImages.vip4avtar,
  },
  {
    level: 5,
    bgColor: `url(${vipBgImages.vip5})`,
    avtar: vipBgImages.vip5avtar,
  },
];
const vipLevels1to5 = ref();
// VIP 等级 6-10 的数据
const vipLevels6to10Bg = [
  {
    level: 6,
    bgColor: `url(${vipBgImages.vip6})`,
    avtar: vipBgImages.vip6avtar,
  },
  {
    level: 7,
    bgColor: `url(${vipBgImages.vip7})`,
    avtar: vipBgImages.vip7avtar,
  },
  {
    level: 8,
    bgColor: `url(${vipBgImages.vip8})`,
    avtar: vipBgImages.vip8avtar,
  },
  {
    level: 9,
    bgColor: `url(${vipBgImages.vip9})`,
    avtar: vipBgImages.vip9avtar,
  },
  {
    level: 10,
    bgColor: `url(${vipBgImages.vip10})`,
    avtar: vipBgImages.vip10avtar,
  },
];
const vipLevels6to10 = ref();
// 初始化vip等级数据
const init = async () => {
  console.log();
  const res = await vipLevels();
  vipLevels1to5.value = res
    .slice(0, 6)
    .map((e: levelsResponse, index: number) => ({
      ...vipLevels1to5Bg[index],
      ...e,
    }));
  vipLevels6to10.value = res
    .slice(6, 11)
    .map((e: levelsResponse, index: number) => ({
      ...vipLevels6to10Bg[index],
      ...e,
    }));
};

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
.ranking-container {
  padding: 24px;
  min-height: 100vh;
  .line {
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, transparent, #f3638e, transparent);
  }

  .vip-content {
    // background: linear-gradient(to bottom, #24274e, transparent);
    border-radius: 14px;
  }
}

.ranking-header {
  text-align: center;
  position: relative;
  margin-bottom: 12px;
  img {
    width: 100%;
    height: auto;
  }
}

.vip-levels-container {
  width: 100%;
}

.vip-level-card {
  border-radius: 14px;
  overflow: hidden;
  color: white;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  width: 500px;
}

.card-privilegio {
  // background-color: rgba(0, 0, 0, 0.5);
  // backdrop-filter: blur(5px);
  border-radius: 14px;
  overflow: hidden;
}

.card-condices {
  // background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  border-bottom-left-radius: 14px;
  border-bottom-right-radius: 14px;
}

.vip-icon-placeholder {
  width: 126px; /* Adjust size as needed */
  // height: 50px; /* Adjust size as needed */
  // background-color: rgba(255, 255, 255, 0.3); /* Placeholder style */
  margin: 0px 30px 0 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* You might want to add the VIP number here later */
}

@media (max-width: 768px) {
  .pa-4 {
    padding: 8px !important;
  }
  .vip-level-card {
    width: 100%;

    background-size: 100% 100% !important;
  }
  .vip-icon-placeholder {
    width: 80px; /* Adjust size as needed */
    // height: 50px; /* Adjust size as needed */
    // background-color: rgba(255, 255, 255, 0.3); /* Placeholder style */
    margin: 0px 0px 0 26px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    /* You might want to add the VIP number here later */
  }
  .ms-4 {
    font-size: 12px;
  }
  .text-h6 {
    font-size: 14px !important;
  }
  .card-condices {
    font-size: 12px;
  }

  // .bonus-item {
  //   .bonus-amount {
  //     font-size: 12px;
  //   }
  // }
}
</style>
