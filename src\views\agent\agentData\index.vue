<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-06 10:37:19
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-16 14:13:01
 * @FilePath: \betdoce-admin\src\views\agent\agentData\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import {
  ElCard,
  ElTable,
  ElForm,
  ElInput,
  ElButton,
  ElPagination,
  ElCol,
  ElRow,
  ElDatePicker,
  ElTabs,
  ElTabPane,
  ElSelect,
  ElOption,
} from "element-plus";
import { fetchGetAgentUserRole } from "@/service/api";
import {
  agentDataWithdrawal,
  agentDataUsers,
  agentDataCommission,
  agentDataStats,
} from "@/service/api";
import { useTable } from "@/hooks/common/table";
import WithdrawDialog from "./components/WithdrawDialog.vue"; // 引入提现弹窗组件
import { InfoFilled } from "@element-plus/icons-vue"; // Import the InfoFilled icon
import { $t } from "@/locales";
import moment from "moment";

// 假設 fetchGetAgentUserRole 和數據獲取接口在這裡可用或已被引入
// import { fetchGetAgentUserRole, fetchCommissionFlow_圖一, fetchUserDataApi, fetchWithdrawRecordApi, fetchCommissionFlow_圖二 } from '@/api/your-api-path'; // 請替換為實際的接口路徑

const canManageAgentLevel = ref<boolean | null>(null);
const activeTab = ref("userData");

// 定义所有表格列配置
const commissionFlowColumns_tab1 = [
  { prop: "index", label: $t("common.index"), width: 64 },
  { prop: "agent_id", label: "代理ID" },
  { prop: "agent_name", label: "代理名称" },
  { prop: "transaction_type_text", label: "交易类型" },
  { prop: "status_text", label: "状态" },
  {
    prop: "formatted_amount",
    label: "交易金额",
    formatter: (row) =>
      `R$ ${((row?.formatted_amount || 0) / 100)?.toFixed(2)}`,
  },
  { prop: "commission_rate", label: "佣金比例(%)", width: 140 },
  {
    prop: "formatted_commission_amount",
    label: "佣金金额",
    formatter: (row) =>
      `R$ ${((row?.formatted_commission_amount || 0) / 100)?.toFixed(2)}`,
  },
  { prop: "order_no", label: "订单号" },
  { prop: "transaction_no", label: "交易号" },
  { prop: "related_agent_name", label: "关联代理商" },
  {
    prop: "occurred_time",
    label: "发生时间",
    width: 180,
    formatter: (row) => moment(row.occurred_time).format("YYYY-MM-DD HH:mm:ss"),
  },
  {
    prop: "complete_time",
    label: "完成时间",
    width: 180,
    formatter: (row) =>
      row.complete_time
        ? moment(row.complete_time).format("YYYY-MM-DD HH:mm:ss")
        : "-",
  },
];

const userDataColumns = [
  { prop: "index", label: $t("common.index"), width: 64 },
  { prop: "user_uuid", label: "用户ID" },
  { prop: "nickname", label: "用户昵称" },
  { prop: "phone", label: "手机号" },
  { prop: "register_time", label: "注册时间", width: 180, formatter: (row) => moment(row.register_time).format("YYYY-MM-DD HH:mm:ss"), },
  { prop: "register_type_text", label: "注册类型" },
  { prop: "inviter_nickname", label: "邀请人昵称" },
  { prop: "inviter_phone", label: "邀请人手机号" },
  {
    prop: "total_recharge_amount",
    label: "总充值金额",
    formatter: (row) =>
      `R$ ${((row?.total_recharge_amount || 0) / 100)?.toFixed(2)}`,
  },
  {
    prop: "total_withdrawal_amount",
    label: "总提现金额",
    formatter: (row) =>
      `R$ ${((row?.total_withdrawal_amount || 0) / 100)?.toFixed(2)}`,
  },
  { prop: "recharge_count", label: "充值次数" },
  { prop: "withdrawal_count", label: "提现次数" },
  {
    prop: "is_first_recharge",
    label: "是否首充",
    formatter: (row) => (row.is_first_recharge === 1 ? "是" : "否"),
  },
  {
    prop: "is_first_withdrawal",
    label: "是否首提",
    formatter: (row) => (row.is_first_withdrawal === 1 ? "是" : "否"),
  },
  { prop: "last_active_time", label: "最后活跃时间", width: 180, formatter: (row) => moment(row.last_active_time).format("YYYY-MM-DD HH:mm:ss"), },
];

const commissionFlowColumns_tab2 = [
  { prop: "index", label: $t("common.index"), width: 64 },
  { prop: "agent_id", label: "代理ID" },
  { prop: "agent_name", label: "代理名称" },
  { prop: "transaction_type_text", label: "交易类型" },
  { prop: "status_text", label: "状态" },
  {
    prop: "formatted_amount",
    label: "交易金额",
    formatter: (row) =>
      `R$ ${((row?.formatted_amount || 0) / 100)?.toFixed(2)}`,
  },
  { prop: "commission_rate", label: "佣金比例(%)", width: 140 },
  {
    prop: "formatted_commission_amount",
    label: "佣金金额",
    formatter: (row) =>
      `R$ ${((row?.formatted_commission_amount || 0) / 100)?.toFixed(2)}`,
  },
  { prop: "order_no", label: "订单号" },
  { prop: "transaction_no", label: "交易号" },
  { prop: "related_agent_name", label: "关联代理商" },
  {
    prop: "occurred_time",
    label: "发生时间",
    width: 180,
    formatter: (row) => moment(row.occurred_time).format("YYYY-MM-DD HH:mm:ss"),
  },
  {
    prop: "complete_time",
    label: "完成时间",
    width: 180,
    formatter: (row) =>
      row.complete_time
        ? moment(row.complete_time).format("YYYY-MM-DD HH:mm:ss")
        : "-",
  },
];

const withdrawRecordColumns = [
  { prop: "index", label: $t("common.index"), width: 64 },
  { prop: "agent_id", label: "代理ID" },
  { prop: "agent_name", label: "代理名称" },
  { prop: "transaction_type_text", label: "交易类型" },
  { prop: "status_text", label: "状态" },
  {
    prop: "formatted_amount",
    label: "提现金额",
    formatter: (row) =>
      `R$ ${((row?.formatted_amount || 0) / 100)?.toFixed(2)}`,
  },
  { prop: "withdrawal_method", label: "提现方式" },
  { prop: "account_type", label: "账户类型" },
  { prop: "account_number", label: "账户号码" },
  { prop: "account_name", label: "账户名称" },
  { prop: "phone", label: "手机号" },
  { prop: "order_no", label: "订单号" },
  { prop: "transaction_no", label: "交易号" },
  { prop: "reject_reason", label: "拒绝原因" },
  {
    prop: "occurred_time",
    label: "申请时间",
    width: 180,
    formatter: (row) => moment(row.occurred_time).format("YYYY-MM-DD HH:mm:ss"),
  },
  {
    prop: "complete_time",
    label: "完成时间",
    width: 180,
    formatter: (row) =>
      row.complete_time
        ? moment(row.complete_time).format("YYYY-MM-DD HH:mm:ss")
        : "-",
  },
];

// 提现记录表单
interface WithdrawRecordForm {
  order_no: string;
  transaction_no: string;
  withdrawal_method: string;
  account_number: string;
  status: number | null;
}

const withdrawRecordForm = ref<WithdrawRecordForm>({
  order_no: "",
  transaction_no: "",
  withdrawal_method: "全部",
  account_number: "",
  status: null,
});

// 图一：代理数据变量
const subAgentCount_tab1 = ref<number | null>(null);
const agentCommission_tab1 = ref<number | null>(null);

// 图一：佣金流水变量和方法
const commissionFlowForm_tab1 = ref({
  agentId: "",
  agentName: "",
  orderNo: "",
  transactionNo: "",
});

interface TableParams {
  page: number;
  size: number;
  [key: string]: any;
}

// 佣金流水表格（图一）
const {
  loading: commissionFlowLoading_tab1,
  data: commissionFlowData_tab1,
  columns: commissionFlowTableColumns_tab1,
  pagination: commissionFlowPagination_tab1,
  getData: getCommissionFlowData_tab1,
  updateSearchParams: updateCommissionFlowSearchParams_tab1,
  resetSearchParams: resetCommissionFlowSearchParams_tab1,
} = useTable<any>({
  apiFn: agentDataCommission,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    agentId: "",
    agentName: "",
    orderNo: "",
    transactionNo: "",
  },
  columns: () => commissionFlowColumns_tab1,
  immediate: false,
});

// 图二：代理数据变量
const subAgentCount_tab2 = ref<number | null>(null);
const userCount_tab2 = ref<number | null>(null);
const profitLoss_tab2 = ref<number | null>(null);
const accountBalance_tab2 = ref<number | null>(null);

// 新增：图二额外总计数据变量
const directRegisterCount = ref<number | null>(null);
const inviteRegisterCount = ref<number | null>(null);
const totalRechargeAmount = ref<number | null>(null);
const totalWithdrawAmount = ref<number | null>(null);
const availableWithdrawAmount = ref<number | null>(null);

// 控制提现弹窗显示的变数
const withdrawDialogVisible = ref(false);

// 图二：用户数据变量和方法
interface UserDataForm {
  user_id: string;
  nickname: string;
  phone: string;
  start_date: string;
  end_date: string;
}

const userDataForm = ref<UserDataForm>({
  user_id: "",
  nickname: "",
  phone: "",
  start_date: "",
  end_date: "",
});

// 用户数据表格（图二）
const {
  loading: userDataLoading,
  data: userDataTableData,
  columns: userDataTableColumns,
  pagination: userDataPagination,
  getData: getUserData,
  updateSearchParams: updateUserDataSearchParams,
  resetSearchParams: resetUserDataSearchParams,
} = useTable<any>({
  apiFn: agentDataUsers,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    user_id: "",
    nickname: "",
    phone: "",
    start_date: "",
    end_date: "",
  },
  columns: () => userDataColumns,
  immediate: false,
});

// 图二：佣金流水变量和方法
const commissionFlowForm_tab2 = ref({
  order_no: "",
  transaction_no: "",
});

// 佣金流水表格（图二）
const {
  loading: commissionFlowLoading_tab2,
  data: commissionFlowData_tab2,
  columns: commissionFlowTableColumns_tab2,
  pagination: commissionFlowPagination_tab2,
  getData: getCommissionFlowData_tab2,
  updateSearchParams: updateCommissionFlowSearchParams_tab2,
  resetSearchParams: resetCommissionFlowSearchParams_tab2,
} = useTable<any>({
  apiFn: agentDataCommission,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    order_no: "",
    transaction_no: "",
  },
  columns: () => commissionFlowColumns_tab2,
  immediate: false,
});

// 提现记录表格（图二）
const {
  loading: withdrawRecordLoading,
  data: withdrawRecordTableData,
  columns: withdrawRecordTableColumns,
  pagination: withdrawRecordPagination,
  getData: getWithdrawRecordData,
  updateSearchParams: updateWithdrawRecordSearchParams,
  resetSearchParams: resetWithdrawRecordSearchParams,
} = useTable<any>({
  apiFn: agentDataWithdrawal,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    order_no: "",
    transaction_no: "",
    withdrawal_method: "全部",
    account_number: "",
    status: null,
  },
  columns: () => withdrawRecordColumns,
  immediate: false,
});

// 获取使用者角色权限的函数
async function fetchGetAgentUserRoleBool() {
  try {
    const res = await fetchGetAgentUserRole({});
    canManageAgentLevel.value = res.data.data.role_id === 1 ? true : false;

    // 获取数据指标
    await fetchAgentDataStats();

    // 根据权限获取对应的数据
    if (canManageAgentLevel.value) {
      getCommissionFlowData_tab1();
    } else {
      // 如果显示图二，预设加载用户数据 Tab 的数据
      getUserData();
    }
  } catch (error) {
    console.error("获取权限失败:", error);
    canManageAgentLevel.value = false; // 获取失败时预设为无权限
    getUserData(); // 获取失败时预设显示图二的用户数据部分
  }
}

// 获取数据指标的函数
async function fetchAgentDataStats() {
  try {
    const res = await agentDataStats({});
    const data = res.data.data;

    if (canManageAgentLevel.value) {
      // 图一的数据映射
      subAgentCount_tab1.value = data.sub_agent_count;
      agentCommission_tab1.value = data.agent_commission;
    } else {
      // 图二的数据映射
      subAgentCount_tab2.value = data.sub_agent_count;
      userCount_tab2.value = data.total_user_count;
      profitLoss_tab2.value = data.profit_loss;
      accountBalance_tab2.value = data.account_balance;

      // 额外总计数据映射
      directRegisterCount.value = data.direct_users;
      inviteRegisterCount.value = data.invite_users;
      totalRechargeAmount.value = data.recharge_amount;
      totalWithdrawAmount.value = data.withdrawal_amount;
      availableWithdrawAmount.value = data.withdrawable_amount;
    }
  } catch (error) {
    console.error("获取数据指标失败:", error);
  }
}

// 在组件挂载时获取权限和初始数据
onMounted(async () => {
  await fetchGetAgentUserRoleBool();

  // 根据权限获取对应的数据
  // if (canManageAgentLevel.value) {
  //   await getCommissionFlowData_tab1();
  // } else {
  //   // 如果显示图二，预设加载用户数据 Tab 的数据
  //   await getUserData();
  // }
});

// 搜索和重置函数
const onCommissionFlowSearch_tab1 = () => {
  updateCommissionFlowSearchParams_tab1({
    ...commissionFlowForm_tab1.value,
    page: 1,
    size: commissionFlowPagination_tab1.pageSize,
  });
  getCommissionFlowData_tab1();
};

const onCommissionFlowReset_tab1 = () => {
  commissionFlowForm_tab1.value = {
    agentId: "",
    agentName: "",
    orderNo: "",
    transactionNo: "",
  };
  resetCommissionFlowSearchParams_tab1();
  getCommissionFlowData_tab1();
};

const onUserDataSearch = () => {
  updateUserDataSearchParams({
    ...userDataForm.value,
    page: 1,
    size: userDataPagination.pageSize,
  });
  getUserData();
};

const onUserDataReset = () => {
  userDataForm.value = {
    user_id: "",
    nickname: "",
    phone: "",
    start_date: "",
    end_date: "",
  };
  resetUserDataSearchParams();
  getUserData();
};

const onCommissionFlowSearch_tab2 = () => {
  updateCommissionFlowSearchParams_tab2({
    ...commissionFlowForm_tab2.value,
    page: 1,
    size: commissionFlowPagination_tab2.pageSize,
  });
  getCommissionFlowData_tab2();
};

const onCommissionFlowReset_tab2 = () => {
  commissionFlowForm_tab2.value = {
    order_no: "",
    transaction_no: "",
  };
  resetCommissionFlowSearchParams_tab2();
  getCommissionFlowData_tab2();
};

const onWithdrawRecordSearch = () => {
  updateWithdrawRecordSearchParams({
    ...withdrawRecordForm.value,
    page: 1,
    size: withdrawRecordPagination.pageSize,
  });
  getWithdrawRecordData();
};

const onWithdrawRecordReset = () => {
  withdrawRecordForm.value = {
    order_no: "",
    transaction_no: "",
    withdrawal_method: "全部",
    account_number: "",
    status: null,
  };
  resetWithdrawRecordSearchParams();
  getWithdrawRecordData();
};

// 处理提现按钮点击
const handleWithdraw = () => {
  console.log("点击提现按钮");
  // 显示提现弹窗
  withdrawDialogVisible.value = true;
};

// 监听 tab 切换
watch(activeTab, (newTab) => {
  switch (newTab) {
    case "userData":
      getUserData();
      break;
    case "commissionFlowTab":
      getCommissionFlowData_tab2();
      break;
    case "withdrawRecord":
      getWithdrawRecordData();
      break;
  }
});
</script>

<template>
  <div>
    <!-- 根据 canManageAgentLevel 判断显示哪个界面 -->
    <div v-if="canManageAgentLevel === true">
      <el-card>
        <h3>代理数据</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="data-card" shadow="hover">
              <div class="data-card-title">下级代理数量</div>
              <div class="data-card-value">{{ subAgentCount_tab1 }}</div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="data-card" shadow="hover">
              <div class="data-card-title">代理佣金</div>
              <div class="data-card-value">R${{ agentCommission_tab1 }}</div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-top: 20px">
        <h3>佣金流水</h3>
        <el-form :inline="true" :model="commissionFlowForm_tab1" class="demo-form-inline">
          <el-form-item label="代理ID">
            <el-input v-model="commissionFlowForm_tab1.agentId" placeholder="请输入代理ID" clearable />
          </el-form-item>
          <el-form-item label="代理名称">
            <el-input v-model="commissionFlowForm_tab1.agentName" placeholder="请输入代理名称" clearable />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="commissionFlowForm_tab1.orderNo" placeholder="请输入订单号" clearable />
          </el-form-item>
          <el-form-item label="交易号">
            <el-input v-model="commissionFlowForm_tab1.transactionNo" placeholder="请输入交易号" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onCommissionFlowSearch_tab1">搜索</el-button>
            <el-button @click="onCommissionFlowReset_tab1">重置</el-button>
            <el-button @click="getCommissionFlowData_tab1">刷新</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="commissionFlowLoading_tab1" :data="commissionFlowData_tab1" style="width: 100%">
          <el-table-column v-for="col in commissionFlowTableColumns_tab1" :key="col.prop" v-bind="col" />
        </el-table>
        <ElPagination v-if="commissionFlowPagination_tab1.total" layout="total,prev,pager,next,sizes"
          v-bind="commissionFlowPagination_tab1" @current-change="commissionFlowPagination_tab1['current-change']"
          @size-change="commissionFlowPagination_tab1['size-change']" />
      </el-card>
    </div>
    <div v-else-if="canManageAgentLevel === false">
      <el-card>
        <h3>代理数据</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="data-card" shadow="hover">
              <div class="data-card-title">下级代理数量</div>
              <div class="data-card-value">{{ subAgentCount_tab2 }}</div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="data-card" shadow="hover">
              <div class="data-card-title">用户人数</div>
              <div class="data-card-value">{{ userCount_tab2 }}</div>
              <div class="data-card-extra">
                直接注册: {{ directRegisterCount ?? 0 }}
              </div>
              <div class="data-card-extra">
                邀请注册: {{ inviteRegisterCount ?? 0 }}
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="data-card" shadow="hover">
              <div class="data-card-title">
                盈亏
                <el-tooltip class="box-item" effect="dark" content="Top Left prompts info" placement="top-start">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="data-card-value">R${{ profitLoss_tab2 }}</div>
              <div class="data-card-extra">
                充值: R${{ totalRechargeAmount ?? 0 }}
              </div>
              <div class="data-card-extra">
                提现: R${{ totalWithdrawAmount ?? 0 }}
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="data-card" shadow="hover">
              <div class="data-card-title">
                账户总额
                <el-tooltip class="box-item" effect="dark" content="Top Left prompts info" placement="top-start">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="data-card-value">
                R${{ accountBalance_tab2 ?? 0 }}
              </div>
              <div class="withdraw-row">
                <div class="data-card-extra">
                  可提现: R${{ availableWithdrawAmount ?? 0 }}
                </div>
                <el-button type="primary" size="small" class="withdraw-button" @click="handleWithdraw">提现</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-top: 20px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="用户数据" name="userData">
            <el-form :inline="true" :model="userDataForm" class="demo-form-inline">
              <el-form-item label="用户ID">
                <el-input v-model="userDataForm.user_id" placeholder="请输入用户ID" clearable />
              </el-form-item>
              <el-form-item label="用户昵称">
                <el-input v-model="userDataForm.nickname" placeholder="请输入用户昵称" clearable />
              </el-form-item>
              <el-form-item label="手机号">
                <el-input v-model="userDataForm.phone" placeholder="请输入手机号" clearable />
              </el-form-item>
              <el-form-item label="注册时间">
                <el-date-picker v-model="userDataForm.registerTimeRange" type="daterange" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="
                    (val) => {
                      if (val) {
                        userDataForm.start_date = val[0];
                        userDataForm.end_date = val[1];
                      } else {
                        userDataForm.start_date = '';
                        userDataForm.end_date = '';
                      }
                    }
                  " />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onUserDataSearch">搜索</el-button>
                <el-button @click="onUserDataReset">重置</el-button>
                <el-button @click="getUserData">刷新</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="userDataLoading" :data="userDataTableData" style="width: 100%">
              <el-table-column v-for="col in userDataTableColumns" :key="col.prop" v-bind="col" />
            </el-table>
            <ElPagination v-if="userDataPagination.total" layout="total,prev,pager,next,sizes"
              v-bind="userDataPagination" @current-change="userDataPagination['current-change']"
              @size-change="userDataPagination['size-change']" />
          </el-tab-pane>

          <el-tab-pane label="佣金流水" name="commissionFlowTab">
            <el-form :inline="true" :model="commissionFlowForm_tab2" class="demo-form-inline">
              <el-form-item label="订单号">
                <el-input v-model="commissionFlowForm_tab2.order_no" placeholder="请输入订单号" clearable />
              </el-form-item>
              <el-form-item label="交易号">
                <el-input v-model="commissionFlowForm_tab2.transaction_no" placeholder="请输入交易号" clearable />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onCommissionFlowSearch_tab2">搜索</el-button>
                <el-button @click="onCommissionFlowReset_tab2">重置</el-button>
                <el-button @click="getCommissionFlowData_tab2">刷新</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="commissionFlowLoading_tab2" :data="commissionFlowData_tab2" style="width: 100%">
              <el-table-column v-for="col in commissionFlowTableColumns_tab2" :key="col.prop" v-bind="col" />
            </el-table>
            <ElPagination v-if="commissionFlowPagination_tab2.total" layout="total,prev,pager,next,sizes"
              v-bind="commissionFlowPagination_tab2" @current-change="commissionFlowPagination_tab2['current-change']"
              @size-change="commissionFlowPagination_tab2['size-change']" />

          </el-tab-pane>

          <el-tab-pane label="提现记录" name="withdrawRecord">
            <el-form :inline="true" :model="withdrawRecordForm" class="demo-form-inline">
              <el-form-item label="订单号">
                <el-input v-model="withdrawRecordForm.order_no" placeholder="请输入订单号" clearable />
              </el-form-item>
              <el-form-item label="交易号">
                <el-input v-model="withdrawRecordForm.transaction_no" placeholder="请输入交易号" clearable />
              </el-form-item>
              <el-form-item label="提现方式">
                <el-select v-model="withdrawRecordForm.withdrawal_method" placeholder="请选择提现方式" clearable>
                  <el-option label="全部" value="全部" />
                  <el-option label="银行卡" value="银行卡" />
                  <el-option label="支付宝" value="支付宝" />
                  <el-option label="微信" value="微信" />
                </el-select>
              </el-form-item>
              <el-form-item label="账号">
                <el-input v-model="withdrawRecordForm.account_number" placeholder="请输入账号" clearable />
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="withdrawRecordForm.status" placeholder="请选择状态" clearable>
                  <el-option label="全部" :value="null" />
                  <el-option label="未验证" :value="0" />
                  <el-option label="已验证" :value="1" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onWithdrawRecordSearch">搜索</el-button>
                <el-button @click="onWithdrawRecordReset">重置</el-button>
                <el-button @click="getWithdrawRecordData">刷新</el-button>
              </el-form-item>
            </el-form>

            <el-table v-loading="withdrawRecordLoading" :data="withdrawRecordTableData" style="width: 100%">
              <el-table-column v-for="col in withdrawRecordTableColumns" :key="col.prop" v-bind="col" />
            </el-table>
            <ElPagination v-if="withdrawRecordPagination.total" layout="total,prev,pager,next,sizes"
              v-bind="withdrawRecordPagination" @current-change="withdrawRecordPagination['current-change']"
              @size-change="withdrawRecordPagination['size-change']" />

          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 提现弹窗 -->
      <WithdrawDialog :visible="withdrawDialogVisible" :account-balance="accountBalance_tab2 ?? undefined"
        @update:visible="withdrawDialogVisible = $event" />
    </div>
  </div>
</template>

<style scoped>
/* 在这里添加组件的样式 */
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

/* 标题样式 */
h3 {
  position: relative;
  padding-left: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #409eff;
  border-radius: 2px;
}

/* 数据卡片样式 */
.data-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  height: 100%;
  /* Make cards fill the height of their parent column */
  display: flex;
  /* Use flexbox for internal layout */
  flex-direction: column;
  /* Stack internal elements vertically */
}

.data-card:hover {
  transform: translateY(-5px);
}

.data-card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.data-card-title .el-icon {
  margin-left: 5px;
  font-size: 16px;
}

.data-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
  /* Add margin for extra info */
}

.data-card-extra {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
  flex-grow: 1;
  /* Allow text to take available space */
}

.withdraw-button {
  margin-top: 0;
  /* Reset top margin */
  width: 100px;
}

/* 提现按钮行样式 */
.withdraw-row {
  display: flex;
  align-items: center;
  margin-top: auto;
  /* Push this row to the bottom of the flex column */
  padding-top: 10px;
  /* Add some padding above */
  border-top: 1px solid #ebeef5;
  /* Optional: Add a separator line */
}

/* 为分页添加一些间距 */
.el-pagination {
  margin-top: 20px;
  justify-content: flex-end;
}

/* 调整 tabs 标签的样式 */
.el-tabs__header {
  margin-bottom: 20px;
}
</style>
