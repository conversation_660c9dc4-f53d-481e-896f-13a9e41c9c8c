<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-04 13:41:29
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-04 17:55:47
 * @FilePath: \betdoce-admin\src\views\channel\channellist\modules\ChannelGameRecord.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-form :inline="true" class="mb-2">
      <el-form-item label="用户ID">
        <el-input v-model="search.user_id" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="search.time"
          type="daterange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
         value-format="x"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%" max-height="400">
      <el-table-column prop="user_id" label="用户ID" />
      <el-table-column prop="transaction_no" label="交易编号" />
      <el-table-column prop="game_id" label="游戏ID" />
      <el-table-column prop="nickname" label="用户昵称" />
      <el-table-column prop="phone" label="手机号码" />
      <el-table-column prop="game_type" label="游戏类型" />
      <el-table-column prop="bet_amount" label="投注金额">
        <template #default="{ row }">
          R$ {{ formatAmount(row.bet_amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="win_amount" label="赢取金额">
        <template #default="{ row }">
          R$ {{ formatAmount(row.win_amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="profit" label="盈亏">
        <template #default="{ row }">
          <span
            :class="{
              'text-success': row.win_amount > row.bet_amount,
              'text-danger': row.win_amount < row.bet_amount,
            }"
          >
            R$ {{ formatAmount(row.win_amount - row.bet_amount) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="transaction_time" label="交易时间" />
    </el-table>
    <el-pagination
      class="mt-2"
      :total="total"
      :page-size="pageSize"
      :current-page="page"
      @current-change="
        (val) => {
          page = val;
          fetchData();
        }
      "
      @size-change="
        (val) => {
          pageSize = val;
          page = 1;
          fetchData();
        }
      "
      layout="total, prev, pager, next, sizes"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { fetchGetChannelGameRecords } from "@/service/api/channel";

const props = defineProps<{ channel: any }>();
const search = ref({
  user_id: "",
  phone: "",
  game_type: "",
  time: [],
});
const tableData = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

const formatAmount = (amount: number) => {
  return ((amount || 0) / 100).toFixed(2);
};

function handleSearch() {
  page.value = 1;
  fetchData();
}

function handleReset() {
  search.value = {
    user_id: "",
    phone: "",
    game_type: "",
    time: [],
  };
  handleSearch();
}

function fetchData() {
  fetchGetChannelGameRecords({
    channel_code: props.channel.channel_code,
    page: page.value,
    size: pageSize.value,
    user_id: search.value.user_id,
    phone: search.value.phone,
    game_type: search.value.game_type,
    start_time: search.value.time?.[0],
    end_time: search.value.time?.[1],
  }).then((response) => {
    if (response?.data) {
      tableData.value = response.data.data;
      total.value = response.data.count;
    }
  });
}

defineExpose({
  fetchData,
});
</script>

<style scoped>
.mb-2 {
  margin-bottom: 20px;
}
.mt-2 {
  margin-top: 20px;
}
.text-success {
  color: #67c23a;
}
.text-danger {
  color: #f56c6c;
}
</style>
