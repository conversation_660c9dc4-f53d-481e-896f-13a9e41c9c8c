<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <InvitationRewardsSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
    </InvitationRewardsSearch>

    <el-card class="sm:flex-1-hidden card-wrapper mt-20px">
      <div class="flex justify-end mb-3">
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @refresh="getData"
        >
          <span style="width: 1px; height: 35px; background: #e5e6eb"></span>
        </TableHeaderOperation>
      </div>
      <div class="h-[calc(100%-100px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
           @current-change="mobilePagination['current-change']"
              @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import moment from "moment";
import InvitationRewardsSearch from "./modules/invitation-rewards-search.vue";
import { useTable } from "@/hooks/common/table";
import type { TableColumnCtx } from "element-plus";
import {getRewardList} from "@/service/api/report"

interface SearchParams {
  parentId: string;
  parentPhone: string;
  registrationSource: string;
}

interface TableItem {
  index: number;
  invited_user_id: number | string;
  invited_phone: string;
  parent_id: number | string;
  parent_phone: string;
  parent_source: string;
  second_parent_id: string;
  third_parent_id: string;
  invite_count: number;
  reward_count: number;
  current_reward_count: number;
  reward_status: string;
  updated_at: string;
}



const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  getDataByPageSize,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable({
  apiFn: getRewardList,
  apiParams: {
    current: 1,
    page:1,
    size: 20,
    parent_id:undefined,
    parent_phone:undefined,
    registration_source:undefined
  },
  columns: () => [
    { type: "index", label: "序号", width: 60 },
    { prop: "invited_user_id", label: "被邀请ID", minWidth: 120 },
    { prop: "invited_phone", label: "被邀请手机", minWidth: 120 },
    { prop: "parent_id", label: "父ID", minWidth: 120 },
    { prop: "parent_phone", label: "父级手机", minWidth: 120 },
    { prop: "parent_source", label: "父级来源", minWidth: 100 },
    { prop: "second_parent_id", label: "二级父ID", minWidth: 120 },
    { prop: "third_parent_id", label: "三级父ID", minWidth: 120 },
    { prop: "invite_count", label: "邀请人数", minWidth: 100 },
    { prop: "reward_count", label: "赠送邀请人数", minWidth: 120 },
    // { prop: "current_reward_count", label: "当前送礼人数", minWidth: 120 },
    { prop: "reward_status", label: "奖励状态", minWidth: 100 },
    {
      prop: "updated_at",
      label: "更新时间",
      minWidth: 160,
      formatter: (
        row: TableItem,
        column: TableColumnCtx<TableItem>,
        cellValue: any,
        index: number,
      ) => formatDateTime(row.updated_at),
    },
  ],
});

function handleReset() {

  resetSearchParams();
}

function handleSearch(params: SearchParams) {

  getData();
}

function formatDateTime(timestamp: string | number) {
  if (!timestamp) return "-";
  return moment(timestamp).format("YYYY-MM-DD HH:mm:ss");
}

onMounted(getData);
</script>

<style lang="scss" scoped>
.min-h-500px {
  min-height: 500px;
}

.flex-col-stretch {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.gap-2px {
  gap: 2px;
}

.overflow-hidden {
  overflow: hidden;
}

.lt-sm\:overflow-auto {
  @media (max-width: 640px) {
    overflow: auto;
  }
}

.card-wrapper {
  border-radius: 0 0 4px 4px;
  border: none;
}

:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
