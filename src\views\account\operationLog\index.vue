<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <OperationLogSearch
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
        }
      "
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation @refresh="getData"
          ><span style="width: 1px; height: 35px; background: #e5e6eb"></span
        ></TableHeaderOperation>
      </template>
    </OperationLogSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <OperationLogDetailDrawer
        v-model:visible="detailDrawerVisible"
        :row-data="detailRow"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { ref } from "vue";
import { useTable } from "@/hooks/common/table";
import OperationLogSearch from "./modules/operation-log-search.vue";
import OperationLogDetailDrawer from "./modules/operation-log-detail-drawer.vue";
import { fetchOperationLogList } from "@/service/api/operationLog";
import { ElButton } from "element-plus";
import { useAuth } from "@/hooks/business/auth";
import { formatNumber } from "@/utils/format";

const { hasAuth } = useAuth();

const detailDrawerVisible = ref(false);
const detailRow = ref<any>(null);

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable({
  apiFn: fetchOperationLogList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 15,
    user_id: undefined,
    operation_type: undefined,
    start_time: undefined,
    end_time: undefined,
  },
  columns: () => [
    {
      prop: "user_id",
      label: "用户ID",
      formatter: (row: any) => row.user_id || "--",
    },
    {
      prop: "operation_type",
      label: "操作类型",
      formatter: (row: any) => row.operation_type || "--",
    },
    {
      prop: "operation_time",
      label: "操作时间",
      formatter: (row: any) => row.operation_time || "--",
    },
    {
      prop: "nickname",
      label: "用户昵称",
      formatter: (row: any) => row.nickname || "--",
    },
    {
      prop: "phone",
      label: "手机号",
      formatter: (row: any) => row.phone || "--",
    },
    {
      prop: "transaction_no",
      label: "交易编号",
      formatter: (row: any) => row.transaction_no || "--",
    },
    {
      prop: "game_uid",
      label: "游戏标识",
      formatter: (row: any) => row.game_uid || "--",
    },
    {
      prop: "game_type",
      label: "游戏类型",
      formatter: (row: any) => row.game_type || "--",
    },
    {
      prop: "game_name",
      label: "游戏名称",
      formatter: (row: any) => row.game_name || "--",
    },
    {
      prop: "bet_amount",
      label: "下注金额",
      width: 120,
      formatter: (row: any) => formatNumber(row.bet_amount) || "--",
    },
    {
      prop: "win_amount",
      label: "赢取金额",
      width: 120,
      formatter: (row: any) => formatNumber(row.win_amount) || "--",
    },
    {
      prop: "get_amount",
      label: "获取金额",
      width: 120,
      formatter: (row: any) =>
        row.amount_type + formatNumber(row.get_amount) || "--",
    },
    {
      prop: "balance",
      label: "余额",
      width: 120,
      formatter: (row: any) => formatNumber(row.balance) || "--",
    },
    {
      prop: "deposit_amount",
      label: "充值金额",
      width: 120,
      formatter: (row: any) => formatNumber(row.deposit_amount) || "--",
    },
    {
      prop: "withdraw_amount",
      label: "提现金额",
      width: 120,
      formatter: (row: any) => formatNumber(row.withdraw_amount) || "--",
    },
    {
      prop: "currency",
      label: "货币",
      formatter: (row: any) => row.currency || "--",
    },
  ],
});

function openDetail(row: any) {
  detailRow.value = row;
  detailDrawerVisible.value = true;
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
