<script setup lang="tsx">
import { ref, reactive, onMounted } from "vue";
import {
  ElMessage,
  ElMessageBox,
  ElTag,
  ElButton,
  ElPopconfirm,
} from "element-plus";
import { fetchGetChannelList, fetchDeleteChannel } from "@/service/api";
import ChannelSearch from "./modules/channel-search.vue";
import ChannelOperateDrawer from "./modules/channel-operate-drawer.vue";
import moment from "moment";
import { useAuth } from "@/hooks/business/auth";
import ChannelDataDialog from "./modules/channel-data-dialog.vue";

const { hasAuth } = useAuth();

const searchParams = reactive({
  keyword: "",
  responsible_by: "",
  status: undefined,
});

const loading = ref(false);
const data = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

const drawerVisible = ref(false);
const operateType = ref<"add" | "edit">("add");
const editingData = ref<any>(null);
const dataDialogVisible = ref(false);
const currentChannel = ref<any>(null);

function getData() {
  loading.value = true;
  fetchGetChannelList({
    ...searchParams,
    page: page.value,
    size: pageSize.value,
  })
    .then((res) => {
      data.value = res?.data?.data || [];
      total.value = res?.data?.count || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleSearch() {
  page.value = 1;
  getData();
}

function handleReset() {
  searchParams.keyword = "";
  searchParams.responsible_by = "";
  searchParams.status = undefined;
  handleSearch();
}

function handleAdd() {
  operateType.value = "add";
  editingData.value = null;
  drawerVisible.value = true;
}

function handleEdit(row: any) {
  operateType.value = "edit";
  editingData.value = { ...row };
  drawerVisible.value = true;
}

async function handleDelete(id: number) {
  await ElMessageBox.confirm("确定要删除该渠道吗？", "提示", {
    type: "warning",
  });
  await fetchDeleteChannel({ id });
  ElMessage.success("删除成功");
  getData();
}

function copyToClipboard(text: string): void {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        ElMessage.success("已复制");
      })
      .catch(() => {
        fallbackCopyToClipboard(text);
      });
  } else {
    fallbackCopyToClipboard(text);
  }
}

function fallbackCopyToClipboard(text: string): void {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.select();
  try {
    document.execCommand("copy");
    ElMessage.success("已复制");
  } catch (err) {
    ElMessage.error("复制失败");
  }
  document.body.removeChild(textArea);
}

function handleViewData(row: any) {
  currentChannel.value = { ...row };
  dataDialogVisible.value = true;
}

onMounted(getData);
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <ChannelSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #table-operation>
        <TableHeaderOperation
          :isNoDelete="true"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
    </ChannelSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full main-table"
          :data="data"
          row-key="id"
        >
          <ElTableColumn prop="id" label="ID" width="60" />
          <ElTableColumn prop="channel_name" label="渠道名称" min-width="120" />
          <ElTableColumn prop="responsible_by" label="负责人" min-width="100" />
          <ElTableColumn prop="contact_info" label="联系方式" min-width="120" />
          <ElTableColumn prop="created_at" label="创建时间" width="200">
            <template #default="{ row }">
              {{ moment(row.created_at).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="channel_link" label="渠道链接" min-width="300">
            <template #default="{ row }">
              <span style="margin-right: 5px">{{ row.channel_link }}</span>
              <ElButton
                plain
                type="primary"
                size="small"
                @click="copyToClipboard(row.channel_link)"
              >
                复制
              </ElButton>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="100">
            <template #default="{ row }">
              <ElTag :type="row.status === 1 ? 'success' : 'info'">{{
                row.status === 1 ? "已启用" : "已停用"
              }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="230" fixed="right">
            <template #default="{ row }">
              <ElButton
                v-if="hasAuth(3)"
                type="primary"
                size="small"
                @click="handleEdit(row)"
                >编辑</ElButton
              >
              <ElPopconfirm
                v-if="hasAuth(2)"
                title="确定删除？"
                @confirm="() => handleDelete(row.id)"
              >
                <template #reference>
                  <ElButton type="danger" size="small">删除</ElButton>
                </template>
              </ElPopconfirm>
              <ElButton size="small" @click="handleViewData(row)"
                >查看数据</ElButton
              >
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="total > 0"
          background
          layout="total,prev,pager,next,sizes"
          :total="total"
          :current-page="page"
          :page-size="pageSize"
          @update:current-page="
            (val) => {
              page = val;
              getData();
            }
          "
          @update:page-size="
            (val) => {
              pageSize = val;
              page = 1;
              getData();
            }
          "
        />
      </div>
      <ChannelOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
      <ChannelDataDialog
        v-model:visible="dataDialogVisible"
        :channel="currentChannel"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
}

.sm\:flex-1-hidden {
  flex: 1;
  min-height: 0;
}
</style>
