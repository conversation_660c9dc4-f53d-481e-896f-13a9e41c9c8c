# WebSocket 登录消息 retry 字段逻辑

## 需求说明
发送登录消息时，`retry` 字段的值应该按照以下规则：
- **第一次登录**：`retry: true`
- **登录成功后的后续登录**：`retry: false`
- **刷新页面、重连 WebSocket 等情况**：`retry: true`

## 实现逻辑

### 1. 状态管理
添加 `hasEverLoggedIn` 标记来跟踪是否曾经登录成功过：

```typescript
private hasEverLoggedIn = false; // 标记是否曾经登录成功过
```

### 2. retry 字段逻辑
在发送登录消息时：

```typescript
const loginMessage = {
  type: "login",
  data: {
    uuid: userInfo.uuid.toString(),
    token: md5Encrypt(userInfo.uuid + "box777studio"),
    in_game: false,
    md5: fingerprint,
    retry: !this.hasEverLoggedIn, // 只有曾经登录成功过才传false，其他情况都传true
  },
};
```

### 3. 登录成功处理
收到登录成功消息时，设置标记：

```typescript
if (type === "login" && success === true) {
  console.log("收到登录成功消息，开始发送心跳");
  this.isLoggedIn = true;
  this.hasEverLoggedIn = true; // 标记曾经登录成功过
  this.startHeartbeat();
}
```

### 4. 状态重置策略
根据不同场景决定是否重置历史登录记录：

#### 需要重置历史记录的场景（retry 重新变为 true）：
- **用户主动登出**：`resetLoginState(true)`
- **页面刷新/重新连接**：`resetLoginState(true)`
- **网络恢复后重连**：`resetLoginState(true)`
- **WebSocket 断开连接**：`resetLoginState(true)`

#### 不重置历史记录的场景（retry 保持 false）：
- **Token 过期**：`resetLoginState()` - 只重置当前状态，保持历史记录

## 场景示例

### 场景1：首次访问页面
1. 页面加载 → `hasEverLoggedIn: false`
2. 发送登录消息 → `retry: true`
3. 登录成功 → `hasEverLoggedIn: true`
4. 后续重连 → `retry: false`

### 场景2：页面刷新
1. 页面刷新 → WebSocket 断开 → `resetLoginState(true)` → `hasEverLoggedIn: false`
2. 重新连接 → 发送登录消息 → `retry: true`
3. 登录成功 → `hasEverLoggedIn: true`

### 场景3：Token 过期
1. Token 过期 → `resetLoginState()` → `hasEverLoggedIn` 保持不变
2. 用户重新登录 → 发送登录消息 → `retry: false`（如果之前登录过）

### 场景4：网络中断恢复
1. 网络中断 → 连接断开
2. 网络恢复 → `resetLoginState(true)` → `hasEverLoggedIn: false`
3. 重新连接 → 发送登录消息 → `retry: true`

## 代码调用示例

```typescript
// 获取登录状态
const status = websocketService.getLoginStatus();
console.log('登录状态:', {
  isLoggedIn: status.isLoggedIn,
  hasEverLoggedIn: status.hasEverLoggedIn,
  loginAttempts: status.loginAttempts
});

// 重置登录状态（保持历史记录）
websocketService.resetLoginState();

// 重置登录状态（清除历史记录）
websocketService.resetLoginState(true);
```

## 优化效果

1. **准确的 retry 标识**：服务器可以根据 retry 字段判断是首次登录还是重连
2. **智能状态管理**：区分不同场景的状态重置需求
3. **更好的用户体验**：避免不必要的重复处理
4. **便于调试**：清晰的状态跟踪和日志输出
