/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-05 11:29:52
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\utils\format.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 格式化日期
 * @param timestamp 时间戳
 * @returns 格式化后的日期字符串
 */
export function formatDate(timestamp: number): string {
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(/\//g, "-");
}

// 格式化數字，支援自定義配置
export const formatNumber = (num: number) => {
  // 檢查是否為 NaN
  if (isNaN(num)) {
    return "0.00";
  }

  const decimals = 2,
    thousandsSeparator = ",",
    decimalSeparator = ".",
    locale = "en-US";

  // 使用 Intl.NumberFormat 進行格式化，啟用千分位分組，並指定 locale
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true, // 啟用千分位分組
  });

  // 直接返回 Intl.NumberFormat 格式化後的結果
  return  formatter.format(num);
};
