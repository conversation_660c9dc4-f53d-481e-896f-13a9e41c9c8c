<template>
  <ElDialog v-model="visible" title="充值人员列表" width="70%" destroy-on-close>
    <div class="recharge-users-list-container">
      <!-- Search Area -->
      <div class="search-area">
        <ElInput placeholder="用户ID" v-model="searchUserId" />
        <ElSelect placeholder="是否首充" v-model="searchIsFirstRecharge">
          <ElOption label="全部" value="" />
          <ElOption label="是" value="1" />
          <ElOption label="否" value="0" />
        </ElSelect>
        <ElSelect placeholder="支付方式" v-model="searchPaymentMethod">
          <ElOption label="全部" value="" />
          <ElOption
            v-for="item in payTypeOptions"
            :label="item"
            :value="item"
          />
          <!-- Add actual payment method options here -->
        </ElSelect>
        <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        <ElButton @click="handleReset">重置</ElButton>
      </div>

      <!-- Table -->
      <ElTable :data="rechargeUsersData" style="width: 100%">
        <ElTableColumn type="index" label="序号" width="60" />
        <ElTableColumn prop="user_id" label="用户ID" />
        <ElTableColumn prop="user_name" label="用户昵称" />
        <ElTableColumn prop="recharge_time" label="充值时间" width="160">
          <template #default="{ row }">
            {{ moment(row.recharge_time).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="recharge_count" label="充值次数" width="100" />
        <ElTableColumn prop="is_first_recharge" label="是否首充" width="100">
          <template #default="{ row }">
            {{ row.is_first_recharge === 1 ? "是" : "否" }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="payment_method" label="支付方式" />
        <ElTableColumn prop="transaction_no" label="交易号" />
        <ElTableColumn prop="recharge_amount" label="充值金额(R$)" />
      </ElTable>

      <!-- Pagination -->
      <div class="pagination-area">
        <ElPagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, watch, PropType } from "vue";
import { fetchGetAgentRechargeUsers } from "@/service/api/agent";
import moment from "moment";
import {
  ElDialog,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";

const props = defineProps({
  visible: { type: Boolean, default: false },
  agentId: {
    type: Number as PropType<number | null | undefined>,
    required: false,
  }, // Agent ID to fetch recharge users for
});

const emit = defineEmits(["update:visible"]);

const visible = ref(props.visible);
const searchUserId = ref("");
const searchIsFirstRecharge = ref("");
const searchPaymentMethod = ref("");
const rechargeUsersData = ref([]); // Placeholder for fetched data
const payTypeOptions = ref(); // 支付方式

// Pagination state
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0); // Placeholder for total count

watch(
  () => props.visible,
  (newVal) => {
    visible.value = newVal;
    if (newVal && props.agentId) {
      // Fetch data when dialog opens
      fetchRechargeUsers(
        props.agentId,
        currentPage.value,
        pageSize.value,
        searchUserId.value,
        searchIsFirstRecharge.value,
        searchPaymentMethod.value,
      );
    }
  },
);

watch(visible, (newVal) => {
  emit("update:visible", newVal);
});

// Placeholder function to fetch data
async function fetchRechargeUsers(
  agentId: number,
  page: number,
  size: number,
  userId?: string,
  isFirstRecharge?: string,
  paymentMethod?: string,
) {
  // console.log(`Fetching recharge users for agent ${agentId}, page ${page}, size ${size}, userId: ${userId}, isFirstRecharge: ${isFirstRecharge}, paymentMethod: ${paymentMethod}`);
  // Replace with actual API call
  const response = await fetchGetAgentRechargeUsers({
    page,
    size,
    agent_id: agentId,
    is_first_recharge:isFirstRecharge,
    user_id:userId,
    payment_method:paymentMethod
  });
  console.log(response);
  rechargeUsersData.value = response?.data?.data?.list; // Clear previous data
  total.value = response?.data?.data?.total; // Reset total
  payTypeOptions.value = response?.data?.data?.payment_methods;
  // Example placeholder data structure (replace with actual API response type)
  // rechargeUsersData.value = [
  //   { userId: 123456789, userNickname: 'UserA', rechargeTime: '2021.08.24 16:34', rechargeCount: 1, isFirstRecharge: '是', paymentMethod: 'Method1', transactionId: 'TXN123', rechargeAmount: 100 },
  //   // ... more data
  // ];
  // total.value = 500; // Example total
}

function handleSearch() {
  currentPage.value = 1;
  if (props.agentId) {
    fetchRechargeUsers(
      props.agentId,
      currentPage.value,
      pageSize.value,
      searchUserId.value,
      searchIsFirstRecharge.value,
      searchPaymentMethod.value,
    );
  }
}

function handleReset() {
  searchUserId.value = "";
  searchIsFirstRecharge.value = "";
  searchPaymentMethod.value = "";
  currentPage.value = 1;
  if (props.agentId) {
    fetchRechargeUsers(
      props.agentId,
      currentPage.value,
      pageSize.value,
      searchUserId.value,
      searchIsFirstRecharge.value,
      searchPaymentMethod.value,
    );
  }
}

function handleSizeChange(val: number) {
  pageSize.value = val;
  currentPage.value = 1;
  if (props.agentId) {
    fetchRechargeUsers(
      props.agentId,
      currentPage.value,
      pageSize.value,
      searchUserId.value,
      searchIsFirstRecharge.value,
      searchPaymentMethod.value,
    );
  }
}

function handleCurrentChange(val: number) {
  currentPage.value = val;
  if (props.agentId) {
    fetchRechargeUsers(
      props.agentId,
      currentPage.value,
      pageSize.value,
      searchUserId.value,
      searchIsFirstRecharge.value,
      searchPaymentMethod.value,
    );
  }
}
</script>

<style scoped>
.recharge-users-list-container {
  padding: 20px;
}

.search-area {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  /* flex-wrap: wrap; Allow wrapping on smaller screens */
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 如果需要针对 Dialog 调整样式，可以在这里添加 */
/*
:deep(.el-dialog__body) {
  padding: 0 20px 20px 20px;
}
*/
</style>
