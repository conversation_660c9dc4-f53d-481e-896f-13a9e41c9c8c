<template>
  <v-container class="bind-info-container" max-width="500">
    <!-- 顶部通知 -->
    <div class="notification-banner">
      <v-icon class="notification-icon">mdi-bell</v-icon>
      <span class="notification-text">
        <PERSON>r favor, preencha as informações corretas, caso contr<PERSON>rio, a retirada
        falhará!
      </span>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 用户名输入 -->
      <div class="form-group">
        <label class="form-label">Nome do usuário:</label>
        <v-text-field
          v-model="formData.userName"
          placeholder="Insira o nome do titular do cartão"
          variant="outlined"
          class="custom-input"
          hide-details
        />
      </div>

      <!-- CPF代码输入 -->
      <div class="form-group">
        <label class="form-label">Código CPF:</label>
        <v-text-field
          v-model="formData.cpfCode"
          placeholder="Insira o seu código CPF"
          variant="outlined"
          class="custom-input"
          hide-details
          maxlength="14"
          @input="formatCPF"
        />
      </div>

      <!-- Pix类型选择 -->
      <div class="form-group">
        <label class="form-label">Tipo Pix</label>
        <v-select
          v-model="formData.pixType"
          :items="pixTypeOptions"
          placeholder="Clique para selecionar Tipo Pix"
          variant="outlined"
          class="custom-select"
          hide-details
          @update:model-value="handlePixTypeChange"
        />
      </div>

      <!-- 动态PIX输入框 -->
      <div v-if="formData.pixType" class="form-group">
        <label class="form-label">{{ getPixTypeLabel() }}</label>

        <!-- CPF输入框 -->
        <v-text-field
          v-if="formData.pixType === 'cpf'"
          v-model="formData.pixValue"
          placeholder="Insira no CPF do pagador real"
          variant="outlined"
          class="custom-input"
          hide-details
          maxlength="14"
          @input="formatPixCPF"
        />

        <!-- 电话输入框 -->
        <div
          v-else-if="formData.pixType === 'phone'"
          class="phone-input-container"
        >
          <v-text-field
            v-model="formData.pixValue"
            placeholder="Número de celular correto"
            variant="outlined"
            class="custom-input phone-input"
            hide-details
            maxlength="15"
            @input="formatPhone"
          >
            <template #prepend-inner>
              <span class="phone-prefix">+55</span>
            </template>
          </v-text-field>
        </div>

        <!-- 邮箱输入框 -->
        <v-text-field
          v-else-if="formData.pixType === 'email'"
          v-model="formData.pixValue"
          placeholder="Por favor introduza o seu e-mail"
          variant="outlined"
          class="custom-input"
          hide-details
          type="email"
        />
      </div>

      <!-- 提交按钮 -->
      <v-btn class="submit-btn" @click="handleSubmit" :loading="isSubmitting">
        Enviar
      </v-btn>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useRouter } from "vue-router";
import { showSuccess, showError } from "@/utils/toast";
import { paymentAccount } from "@/api/user";
import store from "@/store";

const router = useRouter();
const userInfo = computed(() => store.state.auth?.user || {});
// 表单数据
const formData = reactive({
  userName: userInfo.value.username || "",
  cpfCode: userInfo.value.CPF || "",
  pixType: "",
  pixValue: "",
});

// Pix类型选项
const pixTypeOptions = [
  { title: "CPF", value: "cpf" },
  { title: "TELEFONE", value: "phone" },
  { title: "E-MAIL", value: "email" },
];

// 提交状态
const isSubmitting = ref(false);

// CPF格式化
const formatCPF = () => {
  let value = formData.cpfCode.replace(/\D/g, "");
  if (value.length <= 11) {
    value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
    formData.cpfCode = value;
  }
};

// PIX类型变化处理
const handlePixTypeChange = () => {
  formData.pixValue = ""; // 清空之前的输入
};

// 获取PIX类型标签
const getPixTypeLabel = () => {
  switch (formData.pixType) {
    case "cpf":
      return "CPF";
    case "phone":
      return "TELEFONE";
    case "email":
      return "E-MAIL";
    default:
      return "";
  }
};

// PIX CPF格式化
const formatPixCPF = () => {
  let value = formData.pixValue.replace(/\D/g, "");
  if (value.length <= 11) {
    value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
    formData.pixValue = value;
  }
};

// 电话号码格式化
const formatPhone = () => {
  let value = formData.pixValue.replace(/\D/g, "");
  if (value.length <= 11) {
    if (value.length > 2) {
      value = value.replace(/(\d{2})(\d)/, "($1) $2");
    }
    if (value.length > 9) {
      value = value.replace(/(\d{5})(\d)/, "$1-$2");
    }
  }
  formData.pixValue = value;
};

// 验证表单
const validateForm = (): boolean => {
  if (!formData.userName.trim()) {
    showError("Por favor, insira o nome do usuário");
    return false;
  }

  if (!formData.cpfCode.trim()) {
    showError("Por favor, insira o código CPF");
    return false;
  }

  // 验证CPF格式
  const cpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
  if (!cpfRegex.test(formData.cpfCode)) {
    showError("Formato de CPF inválido");
    return false;
  }

  if (!formData.pixType) {
    showError("Por favor, selecione o tipo Pix");
    return false;
  }

  if (!formData.pixValue.trim()) {
    showError("Por favor, preencha as informações do PIX");
    return false;
  }

  // 根据PIX类型验证格式
  if (formData.pixType === "cpf") {
    const pixCpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
    if (!pixCpfRegex.test(formData.pixValue)) {
      showError("Formato de CPF PIX inválido");
      return false;
    }
  } else if (formData.pixType === "phone") {
    const phoneRegex = /^\(\d{2}\) \d{4,5}-\d{4}$/;
    if (!phoneRegex.test(formData.pixValue)) {
      showError("Formato de telefone inválido");
      return false;
    }
  } else if (formData.pixType === "email") {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.pixValue)) {
      showError("Formato de e-mail inválido");
      return false;
    }
  }

  return true;
};

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;

  try {
    // 准备PIX值（根据类型处理）
    let pixValue = formData.pixValue;
    if (formData.pixType === "cpf") {
      pixValue = formData.pixValue.replace(/\D/g, ""); // CPF只保留数字
    } else if (formData.pixType === "phone") {
      pixValue = "+55" + formData.pixValue.replace(/\D/g, ""); // 电话加上+55前缀并移除格式化
    }
    // email类型保持原样

    // 准备API数据
    const apiData = {
      name: formData.userName,
      cpf: formData.cpfCode.replace(/\D/g, ""), // 移除格式化字符，只保留数字
      pix: pixValue,
      account_type: formData.pixType.toUpperCase(),
    };

    // 调用绑卡API
    await paymentAccount(apiData);

    showSuccess("Sucesso");

    // 返回上一页或跳转到其他页面
    router.back();
  } catch (error: any) {
    console.error("绑卡失败:", error);
    const errorMessage =
      error?.message ||
      error?.data?.message ||
      "Por favor, preencha as informações corretas";
    showError(errorMessage);
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped lang="scss">
.bind-info-container {
  min-height: calc(100vh - 64px);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.notification-banner {
  display: flex;
  align-items: flex-start;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 30px;
  //   border-left: 4px solid #fff;
}

.notification-icon {
  color: white;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-text {
  color: white;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 400;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  color: #cfd3dc;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.custom-input {
  :deep() {
    .v-input__control {
      border-radius: 8px !important;
      height: 44px !important;
      background: #1a1b4d;
      .v-field__field {
        height: 44px;
        line-height: 44px;
      }
      .v-field__input {
        padding: 0 16px !important;
        min-height: 44px;
        height: 30px;
      }
    }
    .v-field__overlay {
      border-radius: 8px !important;
    }
    .v-field-label {
      color: #cfd3dc;
    }
    .v-field__outline {
      border-radius: 8px !important;
      color: transparent;
    }
    .v-field__input {
      color: white;
      padding: 16px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }
    .v-field--focused .v-field__outline {
      border-color: rgba(255, 255, 255, 0.3);
    }
  }
}

.custom-select {
  :deep() {
    .v-input__control {
      border-radius: 8px !important;
      height: 44px !important;
      background: #1a1b4d;
      .v-field__field {
        height: 44px;
        line-height: 44px;
      }
      .v-field__input {
        padding: 0 16px !important;
        min-height: 44px;
        height: 30px;
      }
    }
    .v-field__overlay {
      border-radius: 8px !important;
    }
    .v-field-label {
      color: #cfd3dc;
    }
    .v-field__outline {
      border-radius: 8px !important;
      border-color: rgba(255, 255, 255, 0.2);
    }
    .v-field__input {
      color: white;
      padding: 16px;
    }
    .v-field__append-inner {
      .v-icon {
        color: white;
      }
    }
    .v-select__selection-text {
      color: white;
    }
    .v-field--focused .v-field__outline {
      border-color: rgba(255, 255, 255, 0.3);
    }
  }
}

.phone-input-container {
  position: relative;
}

.phone-prefix {
  color: #fff;
  font-weight: 500;
  margin-right: 8px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 14px;
}

.pix-types-info {
  display: flex;
  gap: 16px;
  margin-bottom: 40px;
  margin-top: 8px;
}

.pix-type-item {
  color: white;
  font-size: 14px;
  font-weight: 400;
  opacity: 0.8;
}

.submit-btn {
  background: linear-gradient(0deg, #c9b737, #2abb27) !important;
  color: white !important;
  font-size: 16px;
  font-weight: 600;
  height: 48px;
  border-radius: 20px;
  text-transform: none;
  letter-spacing: 0.5px;
  margin-top: 10px;

  &:hover {
    background: linear-gradient(0deg, #b8a632, #25a324) !important;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &.v-btn--loading {
    opacity: 0.8;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .bind-info-container {
    padding: 16px;
  }

  .notification-banner {
    padding: 10px;
    margin-bottom: 24px;
  }

  .notification-text {
    font-size: 13px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-label {
    font-size: 15px;
    margin-bottom: 6px;
  }

  .pix-types-info {
    margin-bottom: 32px;
    gap: 12px;
  }

  .pix-type-item {
    font-size: 13px;
  }
}
</style>
