<script setup lang="tsx">
import { ElCard, ElTable, ElTableColumn, ElPagination } from 'element-plus';
import { useRoute } from 'vue-router';
import { getSubLevelUsers } from '@/service/api/user';
import type { SubLevelUserParams, SubLevelUser } from '@/service/api/user';
import { useTable } from '@/hooks/common/table';
import { formatDate } from '@/utils/format';
import {router} from "@/router";

const route = useRoute();
const fatherId = route.query.father_id as string;

// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable<SubLevelUserParams>({
  apiFn: getSubLevelUsers,
  apiParams: {
    current: 1,
    size: 20,
    sort: '-id',
    father_id: fatherId
  },
  columns: () => [
    { prop: 'index', label: '序号', width: 64 },
    { prop: 'uuid', label: '用户UUID', minWidth: 120 },
    { prop: 'nickname', label: '昵称', minWidth: 120,formatter: row => row.nickname || '--' },
    { prop: 'phone', label: '手机号', minWidth: 120 },
    { prop: 'invite_code', label: '邀请码', minWidth: 120 },
    {
      prop: 'created_at',
      label: '注册时间',
      width: 180,
      formatter: (row: SubLevelUser) => formatDate(row.created_at)
    }
  ]
});

defineOptions({ name: 'SubLevelUsers' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p><ElButton @click="router.back()"  class="mr-4">返回</ElButton>下级用户列表</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :loading="loading"
            @refresh="getData"
          >
            <span style="width: 1px;height: 35px;background: #e5e6eb;"></span>
          </TableHeaderOperation>
        </div>
      </template>

      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
          />
        </ElTable>

        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
