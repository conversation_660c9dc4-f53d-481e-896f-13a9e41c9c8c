<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <MsgCards :card-data="packages" />
    <MsgSearch
      v-model:model="searchParams"
      :packages="packages"
      @search="searchTable"
      @reset="resetTable"
    >
    </MsgSearch>

    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-80px)]">
        <div class="flex justify-end">
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :isNoDelete="true"
            :isNoAdd="true"
            @refresh="getData"
          />
        </div>
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="tableData"
          row-key="sequence"
          @selection-change="checkedRowKeys = $event"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-start">
          <ElPagination
            :current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
      <MsgOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from "vue";
import { ElButton, ElPopconfirm, ElMessage } from "element-plus";
import MsgSearch from "./modules/msg-search.vue";
import MsgOperateDrawer from "./modules/msg-operate-drawer.vue";
import MsgCards from "./modules/MsgCards.vue";
import { fetchGetSMSManagerList } from "@/service/api/syssms";
import { $t } from "@/locales";
import moment from "moment";
import TableHeaderOperation from "@/components/advanced/table-header-operation.vue";

// 查询参数
const searchParams = reactive({
  package_channel_id: "",
  uuid: "",
  mobile: "",
  content: "",
  ip: "",
  dateRange: "",
  start_time: "",
  end_time: "",
  page: 1,
  size: 10,
});

// 表格数据与状态
const tableData = ref<any[]>([]);
const packages = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const checkedRowKeys = ref([]);

// 1. 所有欄位的原始資料
const allColumns = [
  { prop: "sequence", label: "序号", width: 60 },
  { prop: "uuid", label: "用户ID" },
  { prop: "package_channel_id", label: "套餐频道ID" },
  { prop: "send_channel", label: "发送频道" },
  { prop: "mobile", label: "手机号" },
  { prop: "content", label: "短信内容" },
  {
    prop: "send_status",
    label: "发送状态",
    formatter: (row) => {
      if (row.send_channel === "indiahm_sms") {
        if (row.send_status === 1) return "成功";
        if (row.send_status === 3) return "失败";
      } else if (row.send_channel === "waotp") {
        if (row.send_status === 1) return "成功";
        if (row.send_status === 0) return "失败";
      }
      return row.send_status; // 或者你有其他渠道的显示逻辑
    },
  },
  {
    prop: "callback_status",
    label: "回调状态",
    formatter: (row) => {
      if (row.send_channel === "indiahm_sms") {
        if (row.callback_status === 1) return "成功";
        if (row.callback_status === 3) return "失败";
      } else if (row.send_channel === "waotp") {
        if (row.send_status === 1) return "成功";
        if (row.send_status === 0) return "失败";
      }
      return row.callback_status;
    },
  },
  { prop: "sms_type", label: "短信类型" },
  { prop: "ip", label: "IP地址" },
  {
    prop: "created_at",
    label: "创建时间",
    formatter: (row) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
  },
  {
    prop: "operate",
    label: $t("common.operate"),
    width: 200,
    align: "center",
    fixed: "right",
    formatter: (row) => {
      const pkg = packages.value.find(
        (p) => p.channel_id === row.package_channel_id,
      );
      const canSend =
        pkg && pkg.state === "启用中" && new Date(pkg.end_date) > new Date();
      return (
        <div class="flex-center">
          {canSend && (
            <ElButton
              type="success"
              plain
              size="small"
              onClick={() => handleManualSend(row)}
            >
              人工发送
            </ElButton>
          )}
        </div>
      );
    },
  },
];

// 2. 列設定彈窗的勾選與順序
const columnChecks = ref([
  { prop: "sequence", label: "序号", checked: true },
  { prop: "uuid", label: "用户ID", checked: true },
  { prop: "package_channel_id", label: "套餐频道ID", checked: true },
  { prop: "send_channel", label: "发送频道", checked: true },
  { prop: "mobile", label: "手机号", checked: true },
  { prop: "content", label: "短信内容", checked: true },
  { prop: "send_status", label: "发送状态", checked: true },
  { prop: "callback_status", label: "回调状态", checked: true },
  { prop: "sms_type", label: "短信类型", checked: true },
  { prop: "ip", label: "IP地址", checked: true },
  { prop: "created_at", label: "创建时间", checked: true },
  { prop: "operate", label: $t("common.operate"), checked: true },
]);

// 3. 根據 columnChecks 計算要顯示的 columns
const columns = computed(() => {
  return columnChecks.value
    .filter((item) => item.checked)
    .map((item) => allColumns.find((col) => col.prop === item.prop))
    .filter(Boolean);
});

// 查询表格数据
async function getData() {
  loading.value = true;
  try {
    const params = {
      ...searchParams,
      start_time: searchParams.dateRange[0],
      end_time: searchParams.dateRange[1],
      page: currentPage.value,
      size: pageSize.value,
      package_channel_id: searchParams.package_channel_id,
    };
    delete params.dateRange;
    const res = await fetchGetSMSManagerList(params);
    console.log(res);
    // 统一处理多种返回格式
    let records: any[] = [];
    let count = 0;
    if (res?.data) {
      // 1. 典型格式 { data: { records: [], count: N } }
      if (
        Array.isArray(res.data.data.packages) &&
        Array.isArray(res.data.data.records)
      ) {
        // 3. 你给的复合格式 { data: { packages: [...], records: [...], count: N } }
        records = res.data.data.records;
        count = res.data.data.count ?? res.data.data.records.length ?? 0;
        packages.value = res.data.data.packages;
      }
    }
    tableData.value = records.map((item: any, index: number) => ({
      ...item,
      sequence: (currentPage.value - 1) * pageSize.value + index + 1,
    }));
    total.value = count;
  } finally {
    loading.value = false;
  }
}

// 查询
function searchTable(): void {
  currentPage.value = 1;
  getData();
}
// 重置
function resetTable(): void {
  searchParams.package_channel_id = "";
  searchParams.mobile = "";
  searchParams.uuid = "";
  searchParams.content = "";
  searchParams.ip = "";
  searchParams.dateRange = "";
  currentPage.value = 1;
  getData();
}
// 分页
function handlePageChange(page: number): void {
  currentPage.value = page;
  getData();
}
function handleSizeChange(size: number): void {
  pageSize.value = size;
  currentPage.value = 1;
  getData();
}
// 刷新
function handleRefresh(): void {
  getData();
}

// 编辑/删除/批量删除
const drawerVisible = ref(false);
const operateType = ref("add");
const editingData = ref({});
function handleEdit(row: any): void {
  if (row) {
    operateType.value = "edit";
    editingData.value = { ...row };
    drawerVisible.value = true;
  }
}
async function deleteSms(id: number | string): Promise<void> {
  // 这里调用实际API
  ElMessage.success("删除成功!");
  getData();
}
async function handleDelete(id: number | string): Promise<void> {
  await deleteSms(id);
}
async function handleBatchDelete(): Promise<void> {
  if (!checkedRowKeys.value.length) return;
  // 这里调用实际API
  ElMessage.success("批量删除成功!");
  getData();
}

const handleColumnSettings = () => {
  console.log("列设置");
};

// 人工发送逻辑
function handleManualSend(row: any) {
  operateType.value = "manualSend";
  editingData.value = { ...row };
  drawerVisible.value = true;
}

// 初始化
getData();
</script>

<style lang="scss" scoped>
.min-h-500px {
  min-height: 500px;
}
.flex-col-stretch {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.gap-2px {
  gap: 2px;
}
.overflow-hidden {
  overflow: hidden;
}
.lt-sm\:overflow-auto {
  @media (max-width: 639px) {
    overflow: auto;
  }
}
.sm\:flex-1-hidden {
  @media (min-width: 640px) {
    flex: 1;
    overflow: hidden;
  }
}
.card-wrapper {
  border-radius: 4px;
  .el-card__body {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}
.h-\[calc\(100\%-50px\)\] {
  height: calc(100% - 50px);
}
.sm\:h-full {
  @media (min-width: 640px) {
    height: 100%;
  }
}
.mt-20px {
  margin-top: 20px;
}
.flex {
  display: flex;
}
.justify-start {
  justify-content: flex-start;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.el-button + .el-button {
  margin-left: 8px;
}
</style>
