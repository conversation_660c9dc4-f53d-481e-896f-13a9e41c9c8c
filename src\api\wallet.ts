import request from "./request";

export interface DepositParams {
  amount: number;
  currency: string;
  payment_method: string;
  transaction_no: string;
  user_id: number;
  package_id?: number;
  cpf: string;
  customer_name: string;
}

export interface DepositResponse {
  amount: number;
  currency: string;
  order_id: string;
  pay_url: string;
  qr_code_data: string;
  qr_code_url: string;
  status: string;
  transaction_id: number;
  payment_url: string;
}

/**
 * 提交充值请求
 * @param params 充值参数
 */
export const submitDeposit = (params: DepositParams) => {
  return request.Post<DepositResponse>("/wallet/deposit", params);
};

/**
 * 查询充值状态
 * @param orderId 订单ID
 */
export const queryDepositStatus = (orderId: string) => {
  return request.Get("/wallet/deposit/status", {
    params: {
      order_id: orderId,
      _t: Date.now(),
    },
  });
};

/**
 * 查询充值用户 cpf 回显数据请求
 * @param orderId 订单ID
 */
export const queryUserBinding = (orderId: string) => {
  return request.Get("/wallet/user/binding", {
    params: {
      _t: Date.now(),
    },
  });
};

interface WithdrawalParams {
  amount: number;
  account_num: string;
  account_type: string;
  currency: string;
  user_id: number;
  cpf: string;
}

interface WithdrawalResponse {
  status_code: number;
  data: {
    amount: string;
    display_amount: string;
    fee_percentage: number;
    order_id: string;
    platform_id: string;
    status: string;
    transaction_id: string;
  };
  message?: string;
}

interface WithdrawalRecord {
  id: string;
  title: string;
  time: string;
  amount: number;
  balance: string;
  status: string;
  order_id: string;
}

interface WithdrawalRecordsResponse {
  status_code: number;
  data: {
    total: number;
    records: WithdrawalRecord[];
  };
  message?: string;
}

// 提现
export const withdrawal = (
  params: WithdrawalParams
): Promise<WithdrawalResponse> => {
  return request.Post("/wallet/withdrawal", params);
};

// 提现规则
export const withdrawalLimits = () => {
  return request.Get("/wallet/withdrawal/limits");
};

// 获取提现记录
export const getWithdrawalRecords = (
  params: { user_id: number },
  date = new Date()
): Promise<WithdrawalRecordsResponse> => {
  return request.Get("/wallet/withdrawal/records", {
    params: { ...params, date },
  });
};

// 获取用户提现信息
export const getUserWithdrawalInfo = (
  params: { user_id: number },
  date = new Date()
) => {
  return request.Get("/wallet/withdrawal/userinfo", {
    params: { ...params, date },
  });
};

// 获取商户信息
export const getMerchantInfo = () => {
  return request.Get("/wallet/merchantinfo");
};

// 充值记录接口返回类型
export interface DepositTransaction {
  id: number;
  title: string;
  time: string;
  status: "completed" | "failed";
  amount: number;
  balance: number;
  balance_after: number;
  complete_time: string;
  transaction_id: string;
}

export interface DepositTransactionsResponse {
  status_code: number;
  data: {
    records: any;
    items: DepositTransaction[];
    total: number;
  };
}

export interface PaychannelsResponse {
  map(arg0: (e: any) => { title: any; value: any }): any;
  status_code: number;
  data: [
    {
      channel_name: string;

      channel_type: string;
      id: number;
      status: number;
    }
  ];
}
export interface rechargeLists {
  // map(arg0: (e: any) => { title: any; value: any; }): any;
  status_code: number;
  data: [
    {
      id: number;
      recharge_package_name: string;
      recharge_amount: number;
      bonus_amount: number;
      cash_amount: number;
      package_description: string;
      status: number;
    }
  ];
}

// 获取充值记录
export const getDepositTransactions = (params: {
  user_id: number;
  page: number;
  page_size: number;
}) => {
  return request.Get<DepositTransactionsResponse>(
    "/wallet/deposit/transactions",
    { params: { ...params, _v: Date.now() } }
  );
};

// 获取支付通道列表
export const getPaychannels = () => {
  return request.Get<PaychannelsResponse>("/wallet/paychannels", {});
};

// 获取充值礼包列表
export const getRechargeLists = () => {
  return request.Get("/wallet/recharge/config/list", {});
};
