<script setup lang="ts">
import { ref, reactive } from 'vue';
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElPagination,
} from 'element-plus';

interface SearchParams {
  channel?: string;
  dateRange?: [Date, Date];
}

interface StatisticsRecord {
  date: string;
  channel: string;
  totalAmount: number;
  firstAmount: number;
  secondAmount: number;
  thirdAmount: number;
  totalCommission: number;
  firstCommission: number;
  secondCommission: number;
  thirdCommission: number;
}

// 渠道选项
const channelOptions = [
  { label: '请选择', value: '' },
  { label: '001', value: '001' },
];

// 搜索参数
const searchParams = reactive<SearchParams>({});

// 表格数据
const tableData = ref<StatisticsRecord[]>([]);
const loading = ref(false);

// 统计数据
const summaryData = reactive({
  totalAmount: 0,
  firstAmount: 0,
  secondAmount: 0,
  thirdAmount: 0,
  totalCommission: 0,
  firstCommission: 0,
  secondCommission: 0,
  thirdCommission: 0,
});

// 分页参数
const page = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 生成模拟数据
const generateMockData = () => {
  const data: StatisticsRecord[] = [];
  const startDate = new Date('2025-04-13');

  // 生成更多数据用于分页
  for (let i = 0; i < 30; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    // 生成随机数据，保持与图片中数据规模相似
    const totalAmount = +(Math.random() * 100 + 90).toFixed(4);
    const firstAmount = +(totalAmount * 0.6).toFixed(4);
    const secondAmount = +(totalAmount * 0.3).toFixed(4);
    const thirdAmount = +(totalAmount * 0.1).toFixed(4);

    const totalCommission = +(Math.random() * 50000 + 40000).toFixed(4);
    const firstCommission = +(totalCommission * 0.5).toFixed(4);
    const secondCommission = +(totalCommission * 0.3).toFixed(4);
    const thirdCommission = +(totalCommission * 0.2).toFixed(4);

    data.push({
      date: dateStr,
      channel: '001',
      totalAmount,
      firstAmount,
      secondAmount,
      thirdAmount,
      totalCommission,
      firstCommission,
      secondCommission,
      thirdCommission,
    });
  }

  // 按日期倒序排序
  const sortedData = data.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // 计算当前页数据的统计
  const currentPageData = sortedData.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);

  // 计算总计（使用所有数据计算，而不仅仅是当前页）
  summaryData.totalAmount = +data.reduce((sum, item) => sum + item.totalAmount, 0).toFixed(4);
  summaryData.firstAmount = +data.reduce((sum, item) => sum + item.firstAmount, 0).toFixed(4);
  summaryData.secondAmount = +data.reduce((sum, item) => sum + item.secondAmount, 0).toFixed(4);
  summaryData.thirdAmount = +data.reduce((sum, item) => sum + item.thirdAmount, 0).toFixed(4);
  summaryData.totalCommission = +data.reduce((sum, item) => sum + item.totalCommission, 0).toFixed(4);
  summaryData.firstCommission = +data.reduce((sum, item) => sum + item.firstCommission, 0).toFixed(4);
  summaryData.secondCommission = +data.reduce((sum, item) => sum + item.secondCommission, 0).toFixed(4);
  summaryData.thirdCommission = +data.reduce((sum, item) => sum + item.thirdCommission, 0).toFixed(4);

  total.value = data.length;
  return currentPageData;
};

// 获取表格数据
const getData = async () => {
  loading.value = true;
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    tableData.value = generateMockData();
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  getData();
};

// 重置
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key as keyof SearchParams] = undefined;
  });
  handleSearch();
};

// 表格合计行
const getSummaries = () => {
  return ['合计', '',
    summaryData.totalAmount.toFixed(4),
    summaryData.firstAmount.toFixed(4),
    summaryData.secondAmount.toFixed(4),
    summaryData.thirdAmount.toFixed(4),
    summaryData.totalCommission.toFixed(4),
    summaryData.firstCommission.toFixed(4),
    summaryData.secondCommission.toFixed(4),
    summaryData.thirdCommission.toFixed(4),
  ];
};

// 分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getData();
};

// 页码变化
const handleCurrentChange = (val: number) => {
  page.value = val;
  getData();
};

// 初始化
getData();

defineOptions({ name: 'CommissionStatistics' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="search-card">
      <ElForm :model="searchParams" inline>
        <ElFormItem label="渠道">
          <ElSelect v-model="searchParams.channel" placeholder="请选择" clearable>
            <ElOption
              v-for="option in channelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="日期范围">
          <ElDatePicker
            v-model="searchParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>

    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>打码佣金统计</p>
          <div class="flex gap-12px">
            <ElButton @click="getData">刷新</ElButton>
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          :data="tableData"
          :summary-method="getSummaries"
          show-summary
        >
          <ElTableColumn prop="date" label="日期" min-width="120" />
          <ElTableColumn prop="channel" label="渠道" width="80" />
          <ElTableColumn prop="totalAmount" label="总佣金" min-width="120">
            <template #default="{ row }">
              {{ row.totalAmount.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="firstAmount" label="一级佣金" min-width="120">
            <template #default="{ row }">
              {{ row.firstAmount.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="secondAmount" label="二级佣金" min-width="120">
            <template #default="{ row }">
              {{ row.secondAmount.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="thirdAmount" label="三级佣金" min-width="120">
            <template #default="{ row }">
              {{ row.thirdAmount.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="totalCommission" label="总打码量" min-width="120">
            <template #default="{ row }">
              {{ row.totalCommission.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="firstCommission" label="一级打码量" min-width="120">
            <template #default="{ row }">
              {{ row.firstCommission.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="secondCommission" label="二级打码量" min-width="120">
            <template #default="{ row }">
              {{ row.secondCommission.toFixed(4) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="thirdCommission" label="三级打码量" min-width="120">
            <template #default="{ row }">
              {{ row.thirdCommission.toFixed(4) }}
            </template>
          </ElTableColumn>
        </ElTable>

        <div class="mt-20px flex justify-between items-center">
          <div class="flex gap-24px text-14px">
            <span>总记录数：<span class="text-primary">{{ total }}</span></span>
          </div>
          <ElPagination
            v-if="total"
            v-model:current-page="page"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select {
        width: 160px;
      }

      .el-date-editor {
        width: 260px;
      }
    }
  }
}

:deep(.el-table__footer) {
  font-weight: bold;

  .cell {
    color: var(--el-color-primary);
  }
}
</style>
