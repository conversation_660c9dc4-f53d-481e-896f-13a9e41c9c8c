<script setup lang="ts">
import { ref, reactive } from 'vue';
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn,
  ElTag,
  ElDatePicker,
  ElPagination
} from 'element-plus';

interface SearchParams {
  agent_id?: string;
  date_range?: [string, string];
}

interface TotalStats {
  recharge: number;
  withdraw: number;
  reward: number;
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];

// 搜索参数
const searchParams = reactive<SearchParams>({});

// 分页参数
const page = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref<any[]>([]);

// 统计数据
const totalStats = reactive<TotalStats>({
  recharge: 0,
  withdraw: 0,
  reward: 0
});

// 模拟数据生成函数
const generateMockData = () => {
  const data = [];
  const agentIds = ['A10001', 'A10002', 'A10003', 'A10004'];

  for (let i = 0; i < pageSize.value; i++) {
    const rechargeAmount = +(Math.random() * 10000).toFixed(2);
    const withdrawAmount = +(rechargeAmount * 0.7).toFixed(2);
    const rewardAmount = +(rechargeAmount * 0.1).toFixed(2);
    const agentCommission = +(rechargeAmount * 0.05).toFixed(2);

    data.push({
      agent_id: agentIds[Math.floor(Math.random() * agentIds.length)],
      bill_date: new Date(Date.now() - i * 24 * 60 * 60 * 1000)
        .toISOString()
        .slice(0, 10)
        .replace(/-/g, ''),
      register_count: Math.floor(Math.random() * 100),
      recharge_count: Math.floor(Math.random() * 50),
      recharge_amount: rechargeAmount,
      withdraw_amount: withdrawAmount,
      reward_amount: rewardAmount,
      agent_commission: agentCommission,
      bill_status: Math.random() > 0.3 ? 1 : 0,
      updated_at: new Date(Date.now() - i * 60000).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    });
  }

  // 计算统计数据
  totalStats.recharge = data.reduce((sum, item) => sum + item.recharge_amount, 0);
  totalStats.withdraw = data.reduce((sum, item) => sum + item.withdraw_amount, 0);
  totalStats.reward = data.reduce((sum, item) => sum + item.reward_amount, 0);

  return data;
};

// 获取表格数据
const getData = async () => {
  loading.value = true;
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    tableData.value = generateMockData();
    total.value = 150; // 模拟总数据量
  } finally {
    loading.value = false;
  }
};

// 查看详情
const handleViewDetails = (row: any) => {
  console.log('查看详情:', row);
  // 这里可以添加查看详情的逻辑
};

// 搜索
const handleSearch = () => {
  page.value = 1;
  getData();
};

// 重置
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key as keyof SearchParams] = undefined;
  });
  handleSearch();
};

// 分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getData();
};

// 页码变化
const handleCurrentChange = (val: number) => {
  page.value = val;
  getData();
};

// 初始化
getData();

defineOptions({ name: 'AgentBillManage' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="search-card">
      <ElForm :model="searchParams" inline>
        <ElFormItem label="代理ID">
          <ElInput v-model="searchParams.agent_id" placeholder="请输入代理ID" clearable />
        </ElFormItem>
        <ElFormItem label="日期">
          <ElDatePicker
            v-model="searchParams.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYYMMDD"
            :shortcuts="dateShortcuts"
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>

    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>账单列表</p>
          <div class="flex gap-12px">
            <ElButton @click="getData">刷新</ElButton>
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="tableData"
        >
          <ElTableColumn type="index" label="序号" width="60" />
          <ElTableColumn prop="agent_id" label="代理ID" min-width="120" />
          <ElTableColumn prop="bill_date" label="账单日期" width="100" />
          <ElTableColumn prop="register_count" label="注册人数" width="100">
            <template #default="{ row }">
              <span class="text-primary cursor-pointer" @click="handleViewDetails(row)">
                {{ row.register_count }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="recharge_count" label="充值人数" width="100" />
          <ElTableColumn prop="recharge_amount" label="充值金额" width="120">
            <template #default="{ row }">
              {{ row.recharge_amount.toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="withdraw_amount" label="提现金额" width="120">
            <template #default="{ row }">
              {{ row.withdraw_amount.toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="reward_amount" label="赠收金额" width="120">
            <template #default="{ row }">
              {{ row.reward_amount.toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="agent_commission" label="代理分润" width="120">
            <template #default="{ row }">
              {{ row.agent_commission.toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="bill_status" label="账单状态" width="100">
            <template #default="{ row }">
              <ElTag :type="row.bill_status === 1 ? 'success' : 'info'">
                {{ row.bill_status === 1 ? '已结算' : '未结算' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="updated_at" label="更新时间" width="160" />
        </ElTable>

        <div class="mt-20px flex justify-between items-center">
          <div class="flex gap-24px text-14px">
            <span>累计充值：<span class="text-primary">{{ totalStats.recharge.toFixed(2) }}</span></span>
            <span>累计提现：<span class="text-success">{{ totalStats.withdraw.toFixed(2) }}</span></span>
            <span>累计赠收：<span class="text-warning">{{ totalStats.reward.toFixed(2) }}</span></span>
          </div>
          <ElPagination
            v-if="total"
            v-model:current-page="page"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select,
      .el-date-editor {
        width: 200px;
      }
    }
  }
}

.text-primary {
  color: var(--el-color-primary);
}

.text-success {
  color: var(--el-color-success);
}

.text-warning {
  color: var(--el-color-warning);
}

.cursor-pointer {
  cursor: pointer;
}
</style>
