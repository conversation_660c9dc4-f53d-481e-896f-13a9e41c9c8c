<script setup lang="tsx">
import { ref } from "vue";
import {
  ElButton,
  ElPopconfirm,
  ElMessage,
  ElTag,
  ElIcon,
  ElTabs,
  ElTabPane,
  ElCard,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { EditPen, Delete } from "@element-plus/icons-vue";
import {
  fetchGetActiveChannels,
  fetchDeleteSubscription,
  fetchGetCustomerServiceGroups,
  fetchDeleteCustomerServiceGroup,
  fetchDeleteWhatsappServiceGroup,
  fetchUpdateWhatsappServiceGroup,
  fetchAddWhatsappServiceGroup,
  fetchGetWhatsappServiceGroups,
  enable,
  disable
} from "@/service/api/telegram";
import { $t } from "@/locales";
import { useTable, useTableOperate } from "@/hooks/common/table";
import TelegramDrawer from "./modules/telegram-drawer.vue";
import CustomerServiceDrawer from "./modules/customer-service-drawer.vue";
import WhatsAppDrawer from "./modules/whatsApp-drawer.vue";
import { useAuth } from "@/hooks/business/auth";
import { type TableOperateType } from "@/hooks/common/table";

const { hasAuth } = useAuth();

declare namespace ApiTelegram {
  interface WhatsAppAccount {
    id?: number;
    account_id: string;
    account_name: string;
    phone_number: string;
    added_time: string;
    status: number; // 1: 正常, 0: 已禁用
  }

  interface WhatsAppAccountForm {
    account_id: string;
    phone_number: string;
  }
}

// 定义一个通用的API返回类型
interface ApiResponse {
  error: any; // 或者更具体的错误类型
  data?: any;
  message?: string;
  count?: number;
  list?: any[];
}

defineOptions({ name: "TelegramManagement" });

const activeTab = ref<string | number>("telegram");

// ========== Telegram订阅号管理 ==========
const {
  columns: telegramColumns,
  data: telegramData,
  getData: getTelegramData,
  getDataByPage: getTelegramDataByPage,
  loading: telegramLoading,
  mobilePagination: telegramPagination,
  searchParams: telegramSearchParams,
  resetSearchParams: resetTelegramSearchParams,
  columnChecks: telegramColumnChecks,
} = useTable({
  apiFn: fetchGetActiveChannels,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 10,
    current: 1,
  },
  columns: () => [
    // { prop: "title", label: "频道名称", width: 100 },
    { prop: "chat_id", label: "频道链接" },
    // { prop: "type", label: "类型",
    //   formatter: (row: any) => {
    //     const typeMap = {
    //       channel: "频道",
    //       group: "群组",
    //       supergroup: "超级群"
    //     };
    //     return typeMap[row.type as keyof typeof typeMap] || row.type;
    //   }
    // },
    // { prop: "subscribers", label: "订阅人数", align: "center" },
    {
      prop: "status",
      label: "状态",
      formatter: (row: any) => (
        <ElTag type={row.status === 1 ? "success" : "danger"}>
          {row.status === 1 ? "启用" : "禁用"}
        </ElTag>
      ),
    },
    {
      prop: "operate",
      label: "操作",
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center gap-2">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => editTelegram(row.id)}
            >
              <ElIcon>
                <EditPen />
              </ElIcon>
              编辑
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm
              title="确定要删除这个Telegram吗？"
              onConfirm={() => handleDeleteTelegram(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    <ElIcon>
                      <Delete />
                    </ElIcon>
                    删除
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible: telegramDrawerVisible,
  operateType: telegramOperateType,
  editingData: telegramEditingData,
  handleAdd: handleAddTelegram,
  handleEdit: handleEditTelegram,
  onDeleted: onTelegramDeleted,
  checkedRowKeys: telegramCheckedRowKeys,
} = useTableOperate<any>(telegramData, getTelegramData, "id");

// ========== 客服群管理 ==========
const {
  columns: customerServiceColumns,
  data: customerServiceData,
  getData: getCustomerServiceData,
  getDataByPage: getCustomerServiceDataByPage,
  loading: customerServiceLoading,
  mobilePagination: customerServicePagination,
  searchParams: customerServiceSearchParams,
  resetSearchParams: resetCustomerServiceSearchParams,
  columnChecks: customerServiceColumnChecks,
} = useTable({
  apiFn: fetchGetCustomerServiceGroups,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 10,
    current: 1,
  },
  columns: () => [
    { prop: "id", label: "ID", width: 80 },
    { prop: "group_name", label: "群组名称" },
    // { prop: 'group_id', label: '群组ID', minWidth: 150 },
    {
      prop: "customer_service_count",
      label: "客服数量",
      align: "center",
    },
    { prop: "operating_time_value", label: "运行时间" },
    {
      prop: "invitation_link",
      label: "邀请链接",
      align: "center",
      formatter: (row: any) => row.invitation_link || "",
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row: any) => (
        <ElTag type={row.status === 1 ? "success" : "danger"}>
          {row.status === 1 ? "启用" : "禁用"}
        </ElTag>
      ),
    },
    {
      prop: "operate",
      label: "操作",
      width: 160,
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center gap-2">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => editCustomerService(row.id)}
            >
              <ElIcon>
                <EditPen />
              </ElIcon>
              编辑
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm
              title="确定要删除这个客服群吗？"
              onConfirm={() => handleDeleteCustomerService(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    <ElIcon>
                      <Delete />
                    </ElIcon>
                    删除
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible: customerServiceDrawerVisible,
  operateType: customerServiceOperateType,
  editingData: customerServiceEditingData,
  handleAdd: handleAddCustomerService,
  handleEdit: handleEditCustomerService,
  onDeleted: onCustomerServiceDeleted,
  checkedRowKeys: customerServiceCheckedRowKeys,
} = useTableOperate<any>(customerServiceData, getCustomerServiceData, "id");

// ========== WhatsApp管理 ==========
const {
  columns: whatsAppColumns,
  data: whatsAppData,
  getData: getWhatsAppData,
  getDataByPage: getWhatsAppDataByPage,
  loading: whatsAppLoading,
  mobilePagination: whatsAppPagination,
  searchParams: whatsAppSearchParams,
  resetSearchParams: resetWhatsAppSearchParams,
  columnChecks: whatsAppColumnChecks,
} = useTable({
  apiFn: fetchGetWhatsappServiceGroups,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 10,
    current: 1,
  },
  columns: () => [
    { prop: "account_id", label: "客服账号", width: 120 },
    { prop: "account_name", label: "账号名", width: 100 },
    { prop: "phone", label: "手机号", width: 120 },
    { prop: "created_at", label: "添加时间", width: 150 },
    {
      prop: "status",
      label: "状态",
      formatter: (row: any) => (
        <ElTag type={row.status === 1 ? "success" : "danger"}>
          {row.status_text || (row.status === 1 ? "正常" : "已禁用")}
        </ElTag>
      ),
    },
    {
      prop: "operate",
      label: "操作",
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center gap-2">
          {row.status === 1 ? (
            <>
              {hasAuth(3) && (
                <ElButton
                  type="primary"
                  plain
                  size="small"
                  onClick={() => handleDisableWhatsApp(row.id)}
                >
                  禁用
                </ElButton>
              )}
              {hasAuth(3) && (
                <ElButton
                  type="primary"
                  plain
                  size="small"
                  onClick={() => editWhatsApp(row.id)}
                >
                  <ElIcon>
                    <EditPen />
                  </ElIcon>
                  编辑
                </ElButton>
              )}
            </>
          ) : (
            <>
              {hasAuth(3) && (
                <ElButton
                  type="primary"
                  plain
                  size="small"
                  onClick={() => handleEnableWhatsApp(row.id)}
                >
                  启用
                </ElButton>
              )}
              {hasAuth(2) && (
                <ElPopconfirm
                  title="确定要删除这个WhatsApp客服吗？"
                  onConfirm={() => handleDeleteWhatsApp(row.id)}
                >
                  {{
                    reference: () => (
                      <ElButton type="danger" plain size="small">
                        <ElIcon>
                          <Delete />
                        </ElIcon>
                        删除
                      </ElButton>
                    ),
                  }}
                </ElPopconfirm>
              )}
            </>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible: whatsAppDrawerVisible,
  operateType: whatsAppOperateType,
  editingData: whatsAppEditingData,
  handleAdd: handleAddWhatsApp,
  handleEdit: handleEditWhatsApp,
  onDeleted: onWhatsAppDeleted,
  checkedRowKeys: whatsAppCheckedRowKeys,
} = useTableOperate<any>(whatsAppData, getWhatsAppData, "id");

// 删除Telegram
async function handleDeleteTelegram(id: number) {
  try {
    const { error } = await fetchDeleteSubscription(id);
    if (!error) {
      ElMessage.success("删除成功");
      onTelegramDeleted();
    }
  } catch (error) {
    console.error("删除失败:", error);
    ElMessage.error("删除失败");
  }
}

// 删除客服群
async function handleDeleteCustomerService(id: number) {
  try {
    const { error } = await fetchDeleteCustomerServiceGroup(id);
    if (!error) {
      ElMessage.success("删除成功");
      onCustomerServiceDeleted();
    }
  } catch (error) {
    console.error("删除失败:", error);
    ElMessage.error("删除失败");
  }
}

function editTelegram(id: number) {
  handleEditTelegram(id);
}

function editCustomerService(id: number) {
  handleEditCustomerService(id);
}

function editWhatsApp(id: number) {
  handleEditWhatsApp(id);
}

// 处理WhatsApp抽屉提交
async function handleWhatsAppSubmitted(data: ApiTelegram.WhatsAppAccountForm, operateType: TableOperateType) {
  try {
    if (operateType === "add") {
      const { error } = await fetchAddWhatsappServiceGroup({
        account_id: data.account_id,
        account_name: data.account_id, // 账号名默认使用WhatsApp账号
        phone: data.phone_number,
        status: 0 // 默认为已禁用
      });
      if (!error) {
        ElMessage.success("添加成功");
      }
    } else if (operateType === "edit" && whatsAppEditingData.value) {
      const { error } = await fetchUpdateWhatsappServiceGroup({
        id: whatsAppEditingData.value.id!,
        account_id: data.account_id,
        account_name: data.account_id, // 账号名默认使用WhatsApp账号
        phone: data.phone_number
      });
      if (!error) {
        ElMessage.success("编辑成功");
      }
    }
    getWhatsAppData();
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error("操作失败");
  }
}

// 启用WhatsApp
async function handleEnableWhatsApp(id: number) {
  try {
    const { error } = await enable(id);
    if (!error) {
      ElMessage.success("启用成功");
      getWhatsAppData();
    }
  } catch (error) {
    console.error("启用失败:", error);
    ElMessage.error("启用失败");
  }
}

// 禁用WhatsApp
async function handleDisableWhatsApp(id: number) {
  try {
    const { error } = await disable(id);
    if (!error) {
      ElMessage.success("禁用成功");
      getWhatsAppData();
    }
  } catch (error) {
    console.error("禁用失败:", error);
    ElMessage.error("禁用失败");
  }
}

// 删除WhatsApp
async function handleDeleteWhatsApp(id: number) {
  try {
    const { error } = await fetchDeleteWhatsappServiceGroup(id);
    if (!error) {
      ElMessage.success("删除成功");
      onWhatsAppDeleted();
    }
  } catch (error) {
    console.error("删除失败:", error);
    ElMessage.error("删除失败");
  }
}

// 标签页切换时刷新数据
function handleTabChange(tabName: string | number) {
  if (tabName === "telegram") {
    getTelegramData();
    getCustomerServiceData();
  } else if (tabName === "whatsapp") {
    getWhatsAppData();
  }
}
</script>

<template>
  <div class="min-h-500px gap-16px">
    <ElTabs v-model="activeTab" @tab-change="handleTabChange">
      <ElTabPane label="Telegram客服管理" name="telegram">
        <!-- Telegram列表 -->
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-bold text-lg">Telegram列表</span>
              <ElButton v-if="hasAuth(1)" @click="handleAddTelegram">
                <template #icon>
                  <icon-ic-round-plus class="text-icon" />
                </template>
                添加Telegram
              </ElButton>
            </div>
          </template>
          <div>
            <ElTable
              v-loading="telegramLoading"
              :data="telegramData"
              row-key="id"
              @selection-change="
                telegramCheckedRowKeys = $event.map((row: any) => row.id)
              "
            >
              <ElTableColumn
                v-for="col in telegramColumns"
                :key="col.prop"
                v-bind="col"
              />
            </ElTable>
            <div class="mt-20px flex justify-start">
              <ElPagination
                v-if="telegramPagination.total"
                layout="total,prev,pager,next,sizes"
                v-bind="telegramPagination"
                @current-change="telegramPagination['current-change']"
                @size-change="telegramPagination['size-change']"
              />
            </div>
          </div>
        </ElCard>

        <div style="height: 20px"></div>

        <!-- 客服群列表 -->
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-bold text-lg">客服群列表</span>
              <ElButton v-if="hasAuth(1)" @click="handleAddCustomerService">
                <template #icon>
                  <icon-ic-round-plus class="text-icon" />
                </template>
                添加客服群
              </ElButton>
            </div>
          </template>
          <div>
            <ElTable
              v-loading="customerServiceLoading"
              :data="customerServiceData"
              row-key="id"
              @selection-change="
                customerServiceCheckedRowKeys = $event.map((row: any) => row.id)
              "
            >
              <ElTableColumn
                v-for="col in customerServiceColumns"
                :key="col.prop"
                v-bind="col"
              />
            </ElTable>
            <div class="mt-20px flex justify-start">
              <ElPagination
                v-if="customerServicePagination.total"
                layout="total,prev,pager,next,sizes"
                v-bind="customerServicePagination"
                @current-change="customerServicePagination['current-change']"
                @size-change="customerServicePagination['size-change']"
              />
            </div>
          </div>
        </ElCard>
      </ElTabPane>
      
      <ElTabPane label="WhatsApp管理" name="whatsapp">
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-bold text-lg">WhatsApp客服账号</span>
              <ElButton v-if="hasAuth(1)" @click="handleAddWhatsApp">
                <template #icon>
                  <icon-ic-round-plus class="text-icon" />
                </template>
                添加WhatsApp账号
              </ElButton>
            </div>
          </template>
          <div>
            <ElTable
              v-loading="whatsAppLoading"
              :data="whatsAppData"
              row-key="id"
              @selection-change="
                whatsAppCheckedRowKeys = $event.map((row: any) => row.id)
              "
            >
              <ElTableColumn
                v-for="col in whatsAppColumns"
                :key="col.prop"
                v-bind="col"
              />
            </ElTable>
            <div class="mt-20px flex justify-start">
              <ElPagination
                v-if="whatsAppPagination.total"
                layout="total,prev,pager,next,sizes"
                v-bind="whatsAppPagination"
                @current-change="whatsAppPagination['current-change']"
                @size-change="whatsAppPagination['size-change']"
              />
            </div>
          </div>
        </ElCard>
      </ElTabPane>
    </ElTabs>

    <!-- Telegram抽屉 -->
    <TelegramDrawer
      v-model:visible="telegramDrawerVisible"
      :operate-type="telegramOperateType as 'add' | 'edit'"
      :row-data="telegramEditingData"
      @submitted="getTelegramData"
    />

    <!-- 客服群抽屉 -->
    <CustomerServiceDrawer
      v-model:visible="customerServiceDrawerVisible"
      :operate-type="customerServiceOperateType as 'add' | 'edit'"
      :row-data="customerServiceEditingData"
      @submitted="getCustomerServiceData"
    />

    <!-- WhatsApp抽屉 -->
    <WhatsAppDrawer
      v-model:visible="whatsAppDrawerVisible"
      :operate-type="whatsAppOperateType as 'add' | 'edit'"
      :row-data="whatsAppEditingData"
      @submitted="handleWhatsAppSubmitted"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 4px;
  border: none;

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.card-wrapper {
  min-height: 400px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
