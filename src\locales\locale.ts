/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: As<PERSON><PERSON><PERSON>j <EMAIL>
 * @LastEditTime: 2025-06-10 16:53:23
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\locales\locale.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import zhCN from './langs/zh-cn';

const locales: Record<App.I18n.LangType, App.I18n.Schema> = {
  'zh-CN': zhCN
};

export default locales;
