<template>
  <div class="jackpot-balance-container">
    <!-- 金币雨背景 -->
    <div class="coin-rain-background"></div>
    
    <div class="jackpot-amount">
      <span class="amount">
        <span class="static-part">{{ staticPart }}</span>
        <span class="animated-part">{{ animatedPart }}</span>
      </span>
    </div>
    <v-btn class="jackpot-title" @click="handleApostaClick()"></v-btn>
    <!-- <div ></div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import coinGif from "@/assets/images/coin2.gif";

interface NumberFormatOptions {
  decimals?: number;
  thousandsSeparator?: string;
  decimalSeparator?: string;
  locale?: string;
}

// 定義默認的數字格式化選項
const defaultFormatOptions = {
  decimals: 2,
  locale: "en-US",
  thousandsSeparator: ",",
  decimalSeparator: ".",
} as const;

// 格式化數字，支援自定義配置
const formatNumber = (num: number, options: NumberFormatOptions = {}) => {
  // 檢查是否為 NaN
  if (isNaN(num)) {
    return "0.00";
  }

  const {
    decimals = 2,
    thousandsSeparator = ",",
    decimalSeparator = ".",
    locale = "en-US",
  } = options;

  // 使用 Intl.NumberFormat 進行格式化，啟用千分位分組，並指定 locale
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true, // 啟用千分位分組
  });

  // 直接返回 Intl.NumberFormat 格式化後的結果
  return formatter.format(num);
};

const router = useRouter();
const store = useStore();

// 使用 getters 獲取獎池數據
const currentBalance = computed(() => store.getters["jackpot/currentBalance"]);

// 當前顯示的餘額（用於動畫）
const currentDisplayBalance = ref(0);

// 拆分數字為靜態部分和動畫部分
const staticPart = ref("");
const animatedPart = ref("");

// 將原始金額轉換為顯示金額（除以100）
const convertToDisplayAmount = (amount: number): number => {
  return amount / 100;
};

// 更新數字顯示
const updateDisplay = (value: number) => {
  // 在這裡增加檢查，確保 currentDisplayBalance.value 是有效數字
  if (
    typeof currentDisplayBalance.value !== "number" ||
    isNaN(currentDisplayBalance.value)
  ) {
    // console.warn('updateDisplay - currentDisplayBalance is not a valid number, skipping update:', currentDisplayBalance.value);
    return;
  }

  if (typeof value !== "number") {
    // console.log('updateDisplay received non-number value:', value);
    return;
  }

  const displayValue = convertToDisplayAmount(value);
  // console.log('updateDisplay - displayValue before formatting:', displayValue);
  const formattedNumber = formatNumber(displayValue, defaultFormatOptions);
  const oldDisplayValue = convertToDisplayAmount(currentDisplayBalance.value);
  const oldFormatted = formatNumber(oldDisplayValue, defaultFormatOptions);

  // 如果是初始化，整個數字都是靜態部分
  if (currentDisplayBalance.value === 0 && staticPart.value === "") {
    staticPart.value = formattedNumber;
    animatedPart.value = "";
    return;
  }

  // 找出變動的部分
  let i = 0;
  while (
    i < formattedNumber.length &&
    i < oldFormatted.length &&
    formattedNumber[i] === oldFormatted[i]
  ) {
    i++;
  }

  staticPart.value = formattedNumber.substring(0, i);
  animatedPart.value = formattedNumber.substring(i);
};

// 計算顯示的餘額
const displayBalance = computed(() => {
  return formatNumber(
    convertToDisplayAmount(currentDisplayBalance.value),
    defaultFormatOptions
  );
});

// 動畫函數
const animateBalance = (start: number, end: number) => {
  const duration = 1000; // 動畫持續時間（毫秒）
  const startTime = Date.now();

  const animate = () => {
    const now = Date.now();
    const elapsed = now - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用緩動函數使動畫更自然
    const easeProgress = easeOutQuad(progress);

    // 計算當前值（保持原始值進行動畫，在顯示時再轉換）
    const currentValue = start + (end - start) * easeProgress;
    currentDisplayBalance.value = currentValue;
    updateDisplay(currentValue);

    // 如果動畫未完成，繼續動畫
    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  // 開始動畫
  requestAnimationFrame(animate);
};

// 初始化獎池金額
const initJackpotBalance = async () => {
  try {
    // console.log('開始初始化獎池金額')
    const response = await store.dispatch("jackpot/fetchJackpotBalance");
    // console.log('獲取獎池金額響應:', response)

    if (
      response &&
      typeof response.amount === "number" &&
      !isNaN(response.amount)
    ) {
      // console.log('設置獎池金額:', response.amount)
      // 設置初始值（保持原始值）
      currentDisplayBalance.value = response.amount;
      // 初始化時，整個數字都是靜態部分
      staticPart.value = formatNumber(
        convertToDisplayAmount(response.amount),
        defaultFormatOptions
      );
      animatedPart.value = "";
    } else {
      // console.warn('無效的獎池金額響應:', response)
      // 設置默認值
      currentDisplayBalance.value = 0;
      staticPart.value = formatNumber(0, defaultFormatOptions);
      animatedPart.value = "";
    }
  } catch (error) {
    // console.error('初始化獎池金額失敗:', error)
    // 設置默認值
    currentDisplayBalance.value = 0;
    staticPart.value = formatNumber(0, defaultFormatOptions);
    animatedPart.value = "";
  }
};

// 監聽獎池餘額變化
// 引入 lodash-es throttle
import { throttle } from "lodash-es";

const throttledAnimateBalance = throttle((oldBalance: number, newBalance: number) => {
  animateBalance(oldBalance, newBalance);
}, 200);

watch(
  currentBalance,
  (newBalance, oldBalance) => {
    if (newBalance !== oldBalance) {
      throttledAnimateBalance(oldBalance, newBalance);
    }
  },
  { immediate: false }
);

// 金币雨相关状态
let isRaining = false;
let animationId: number | null = null;
let coins: Coin[] = [];
let windowWidth = window.innerWidth;
let windowHeight = window.innerHeight;

// 金币配置
const coinConfig = {
  minSize: 15,
  maxSize: 30,
  minSpeed: 1,
  maxSpeed: 3,
  minRotationSpeed: 0.5,
  maxRotationSpeed: 2,
  maxCoins: 100, // 增加金币数量从50到100
  spawnInterval: 80, // 减少生成间隔从100ms到80ms，让金币生成更频繁
  fadeStartDistance: 0.7, // 开始淡出的距离比例
  fadeEndDistance: 1.2, // 完全淡出的距离比例
};

// 检测是否为移动端
const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         window.innerWidth <= 768;
};

// 获取移动端优化的配置
const getOptimizedConfig = () => {
  if (isMobile()) {
    return {
      ...coinConfig,
      maxCoins: 60, // 移动端增加金币数量从30到60
      spawnInterval: 120, // 移动端生成间隔从150ms减少到120ms
      minSize: 10, // 减小金币尺寸
      maxSize: 20,
      minSpeed: 0.8,
      maxSpeed: 2,
    };
  }
  return coinConfig;
};

// 金币对象池复用
let coinPool: Coin[] = [];

// 金币类
class Coin {
  element: HTMLDivElement;
  size: number;
  x: number;
  y: number;
  speed: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  fadeDelay: number;

  constructor() {
    this.element = document.createElement("div");
    this.element.className = "background-coin";
    
    const optimizedConfig = getOptimizedConfig();
    this.size =
      Math.random() * (optimizedConfig.maxSize - optimizedConfig.minSize) +
      optimizedConfig.minSize;
    this.x = Math.random() * windowWidth;
    this.y = -this.size;
    this.speed =
      Math.random() * (optimizedConfig.maxSpeed - optimizedConfig.minSpeed) +
      optimizedConfig.minSpeed;
    this.rotation = 0;
    this.rotationSpeed =
      Math.random() *
        (optimizedConfig.maxRotationSpeed - optimizedConfig.minRotationSpeed) +
      optimizedConfig.minRotationSpeed;
    this.element.style.width = `${this.size}px`;
    this.element.style.height = `${this.size}px`;
    this.element.style.backgroundImage = `url('${coinGif}')`;
    this.element.style.backgroundSize = "contain";
    this.element.style.backgroundRepeat = "no-repeat";
    this.element.style.position = "absolute";
    this.element.style.pointerEvents = "none";
    this.element.style.zIndex = "1";
    this.opacity = 1; // 背景金币透明度较低
    this.fadeDelay = Math.random() * 0.3;

    // 将金币添加到金币雨容器中
    const rainContainer = document.querySelector(".coin-rain-background");
    if (rainContainer) {
      rainContainer.appendChild(this.element);
    }

    this.update();
  }

  update(): boolean {
    this.y += this.speed;
    this.rotation += this.rotationSpeed;

    // 计算下落距离的比例
    const fallRatio = this.y / windowHeight;

    // 根据下落距离计算透明度
    if (fallRatio >= coinConfig.fadeStartDistance + this.fadeDelay) {
      const fadeProgress =
        (fallRatio - (coinConfig.fadeStartDistance + this.fadeDelay)) /
        (coinConfig.fadeEndDistance - coinConfig.fadeStartDistance);

      this.opacity = 0.3 * (1 - easeOutCubic(Math.min(1, fadeProgress)));
      this.element.style.opacity = Math.max(0, this.opacity).toString();

      // 随着淡出，稍微缩小金币
      const scale = 1 - 0.2 * fadeProgress;
      this.element.style.transform = `translate(${this.x}px, ${this.y}px) rotate(${this.rotation}deg) scale(${scale})`;
    } else {
      this.element.style.transform = `translate(${this.x}px, ${this.y}px) rotate(${this.rotation}deg)`;
    }

    // 超出屏幕底部或完全透明时移除
    if (this.y > windowHeight + this.size || this.opacity <= 0.01) {
      this.remove();
      return false;
    }
    return true;
  }

  reset() {
    const optimizedConfig = getOptimizedConfig();
    this.size = Math.random() * (optimizedConfig.maxSize - optimizedConfig.minSize) + optimizedConfig.minSize;
    this.x = Math.random() * windowWidth;
    this.y = -this.size;
    this.speed = Math.random() * (optimizedConfig.maxSpeed - optimizedConfig.minSpeed) + optimizedConfig.minSpeed;
    this.rotation = 0;
    this.rotationSpeed = Math.random() * (optimizedConfig.maxRotationSpeed - optimizedConfig.minRotationSpeed) + optimizedConfig.minRotationSpeed;
    this.element.style.width = `${this.size}px`;
    this.element.style.height = `${this.size}px`;
    this.opacity = 1; // 初始为1
    this.element.style.opacity = this.opacity.toString();
    this.fadeDelay = Math.random() * 0.3;
    this.element.style.transform = `translate(${this.x}px, ${this.y}px) rotate(${this.rotation}deg)`;
  }
  remove() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
      const index = coins.indexOf(this);
      if (index > -1) {
        coins.splice(index, 1);
      }
      coinPool.push(this); // 回收到池
    }
  }
}

function createCoin() {
  let coin: Coin;
  if (coinPool.length > 0) {
    coin = coinPool.pop()!;
    coin.reset();
    // 重新添加到 DOM
    const rainContainer = document.querySelector(".coin-rain-background");
    if (rainContainer && !rainContainer.contains(coin.element)) {
      rainContainer.appendChild(coin.element);
    }
  } else {
    coin = new Coin();
  }
  coins.push(coin);
}

// 缓动函数
function easeOutCubic(x: number): number {
  return 1 - Math.pow(1 - x, 3);
}

// 开始金币雨
function startCoinRain() {
  if (isRaining) return;
  isRaining = true;

  const optimizedConfig = getOptimizedConfig();
  let lastSpawnTime = 0;
  let frameCount = 0;
  function animate(timestamp: number) {
    frameCount++;
    // 节流金币生成
    if (
      frameCount % 2 === 0 &&
      timestamp - lastSpawnTime > optimizedConfig.spawnInterval &&
      coins.length < optimizedConfig.maxCoins
    ) {
      const spawnCount = isMobile() ? Math.floor(Math.random() * 2) + 1 : Math.floor(Math.random() * 3) + 2;
      for (let i = 0; i < spawnCount; i++) {
        if (coins.length < optimizedConfig.maxCoins) {
          createCoin();
        }
      }
      lastSpawnTime = timestamp;
    }
    coins.forEach((coin) => coin.update());
    if (isRaining) {
      animationId = requestAnimationFrame(animate);
    }
  }
  animationId = requestAnimationFrame(animate);
}

// 停止金币雨
function stopCoinRain() {
  isRaining = false;
  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }

  // 清空所有金币
  coins.forEach((coin) => coin.remove());
  coins = [];
}

// 窗口大小改变时更新尺寸
function handleResize() {
  windowWidth = window.innerWidth;
  windowHeight = window.innerHeight;
}

// resize 事件防抖
let resizeTimeout: number | null = null;
function handleResizeDebounced() {
  if (resizeTimeout) clearTimeout(resizeTimeout);
  resizeTimeout = window.setTimeout(() => {
    windowWidth = window.innerWidth;
    windowHeight = window.innerHeight;
  }, 100); // 100ms 防抖
}

// 组件挂载时初始化数据
onMounted(() => {
  initJackpotBalance();
  // 启动金币雨背景
  startCoinRain();
  // 添加窗口大小改变监听（防抖）
  window.addEventListener("resize", handleResizeDebounced);
});

// 组件卸载时清理
onUnmounted(() => {
  stopCoinRain();
  window.removeEventListener("resize", handleResizeDebounced);
});

// 緩動函數
const easeOutQuad = (t: number) => {
  return t * (2 - t);
};

// 獎池點擊
const handleApostaClick = () => {
  router.push(`/aposta`);
};
</script>

<style lang="scss" scoped>

@keyframes scaleAnimation {
  0% {
    transform: scale(1); /* 初始大小 */
  }
  50% {
    transform: scale(1.2); /* 放大到1.5倍 */
  }
  100% {
    transform: scale(1); /* 恢复原始大小 */
  }
}

.jackpot-balance-container {
  width: 100%;
  height: 100%;
  margin-left: 50px;
  padding-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative; /* 添加相对定位 */

  /* 金币雨背景容器 */
  .coin-rain-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
  }

  .jackpot-title {
    font-size: 24px;
    width: 180px;
    height: 80px;
    font-weight: 600;
    margin-top: -30px;
    margin-bottom: 4px;
    color: #fff;
    border-radius: 20px;
    box-shadow: none;
    background: url("@/assets/images/jackpot.png") no-repeat;
    background-size: 100% 100%;
    animation: scaleAnimation 2s infinite; /* 动画持续2秒，循环播放 */
    position: relative;
    z-index: 2; /* 确保按钮在金币雨之上 */
  }

  .jackpot-amount {
    font-size: 68px;
    font-weight: 700;
    position: relative;
    z-index: 2; /* 确保金额显示在金币雨之上 */

    .amount {
      color: #fff;
      display: inline-flex;
      align-items: baseline;

      .static-part {
        color: #fff;
      }

      .animated-part {
        color: #fbd859;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px rgba(251, 216, 89, 0.3);
      }
    }
  }
}

/* 背景金币样式 */
:deep(.background-coin) {
  position: absolute;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: opacity 0.3s ease-out;
  will-change: transform, opacity;
}

/* 移動端樣式調整 */
@media (max-width: 768px) {
  .jackpot-balance-container {
    padding: 8px;
    width: 100%;
    margin-left: 30px;

    .jackpot-title {
      font-size: 12px;
      margin-bottom: 0px;
      height: 36px;
      width: 80px;
      background-size: 100% 100%;
      margin-top: -5px;
    }

    .jackpot-amount {
      font-size: 24px;

      .amount {
        .animated-part {
          text-shadow: none; // 移動端移除發光效果
        }
      }
    }
  }

  /* 移动端金币雨优化 */
  :deep(.background-coin) {
    transition: opacity 0.2s ease-out;
    will-change: transform, opacity;
  }
}
</style>

