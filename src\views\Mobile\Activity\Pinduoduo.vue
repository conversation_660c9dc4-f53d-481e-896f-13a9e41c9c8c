<template>
  <div class="pdd-activity" :class="{ help: status === 'help' }">
    <div class="blck-bg" v-if="status === 'ok'"></div>
    <!-- 返回按钮 -->
    <div class="back-button" @click="handleBack">
      <v-icon>mdi-arrow-left</v-icon>
    </div>
    <template v-if="status !== 'ok'">
      <div class="reward-card" v-if="status === 'start'">
        <div class="reward-title-tip">
          Open the gift package and receive it immediately
        </div>
        <div class="reward-amount">
          R${{ activityData?.reward_digital || "100-1000" }}
        </div>
        <div
          class="open-button"
          @click="handleOpen"
          :class="{ disabled: isAnimating }"
        >
          <span>Open</span>
          <div class="button-glow"></div>
        </div>
        <div class="triangle"></div>
        <div class="open-tip">Open and withdraw immediately</div>
      </div>

      <div class="reward-card-help" v-if="status === 'help'">
        <div class="reward-header">
          <div class="header-radius">
            <div class="header-main">
              <div class="amount-text">
                <span>R$</span
                >{{
                  activityStatus?.target_amount ||
                  activityData?.max_withdrawal_amount ||
                  "1000"
                }}
              </div>
              <v-progress-linear
                :model-value="
                  activityStatus
                    ? (activityStatus.accumulated_amount /
                        activityStatus.target_amount) *
                      100
                    : 0
                "
                rounded
                height="10"
                color="#fff"
                class="progress-bar"
              ></v-progress-linear>
            </div>
          </div>
        </div>

        <div class="help-text">
          <span v-if="activityStatus.status === 1"
            >You are only R${{
              activityStatus?.target_amount -
                activityStatus?.accumulated_amount || "0"
            }}
            away from withdrawing
          </span>
          <span v-else>
            Click Help to help your friend get
            {{ activityData?.reward_digital || "100-1000" }} rupees, while you
            will also get a random bonus of R$5-R$1000.
          </span>
        </div>
        <v-btn class="share-btn text-subtitle-1" @click="handleInvite">
          Inviting assistance
        </v-btn>
        <div class="countdown">Countdown: {{ formatTime(countdown) }}</div>
        <div class="final-text">
          <span class="final-text-title">Bonus claiming rules:</span>
          <div>
            1: You need to click "inviting assistance" and then share it with
            your friends<br />
            2: When your friends receive the link you shared, ask them to click
            "Assistance" <br />
            3: When your friends click "Assistance", you and your friends will
            directly receive a bonus of
            {{ activityData?.reward_digital || "100-1000" }} in your account,
            and you will also receive a random bonus. When you click the random
            bonus, the random bonus you receive will be added directly to your
            bonus progress. When your bonus progress is completed, you will
            receive all the bonuses and can withdraw them directly
          </div>
        </div>
      </div>
      <div class="reward-box" :class="{ 'help-box': status === 'help' }"></div>
    </template>
    <!-- 奖励卡片 -->
    <template v-else>
      <div class="reward-card-help card-ok">
        <div class="ok-img">
          <img src="@/assets/images/h5/pdd-ok-tp.png" />
        </div>
        <div class="reward-header">
          <div class="header-radius">
            <div class="header-main">
              <div class="amount-text">
                <span>R$</span
                >{{
                  activityStatus?.target_amount ||
                  activityData?.max_withdrawal_amount ||
                  "1000"
                }}
              </div>
            </div>
          </div>
        </div>

        <div class="help-text">
          <span
            >Congratulations on receiving R${{ activityStatus?.target_amount }}
          </span>
        </div>
        <div class="final-text mt-8">
          <div>
            hare the bonus you received on the official channel, and the bonus
            will be directly credited to your account
          </div>
        </div>
        <div class="triangle"></div>
        <v-btn class="share-btn text-subtitle-1" @click="handleShare">
          Share to channe
        </v-btn>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import {
  getActivityStatus,
  joinActivity,
  type ActivityStatusRecord,
} from "@/api/activity";
import type { PddActivityContent } from "@/api/home";
import { showError, showSuccess } from "@/utils/toast";

// 复制文本到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // 如果 navigator.clipboard 不可用，使用传统方法
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.style.position = "fixed";
    textarea.style.opacity = "0";
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand("copy");
      document.body.removeChild(textarea);
      return true;
    } catch (err) {
      document.body.removeChild(textarea);
      return false;
    }
  }
};

const router = useRouter();
const store = useStore();

const status = ref("start");
const activityData = ref<PddActivityContent | null>(null);
const countdown = ref(16 * 60 * 1000); // 16 minutes in milliseconds
const isAnimating = ref(false);
const activityStatus = ref<ActivityStatusRecord | null>(null);

// 设置页面标题
onMounted(async () => {
  // 从 sessionStorage 获取活动数据
  const storedData = sessionStorage.getItem("pddActivityData");
  console.log(storedData);
  if (storedData) {
    const parsedData = JSON.parse(storedData);
    activityData.value = {
      ...parsedData,
      ...JSON.parse(parsedData.content),
    };
    // 获取用户活动状态
    const response = await getActivityStatus({
      user_id: store.state.auth.user.id,
      activity_id: parsedData.id,
    });
    if (response) {
      let statusArr = ["start", "help", "start", "ok"];
      // 设置倒计时
      const now = new Date().getTime();
      const endTime = response.end_time;
      if (endTime > now) {
        countdown.value = endTime - now;
      }
      status.value = statusArr[response.status];
      if ([1, 3].includes(response.status)) {
        activityStatus.value = response;
      }
    }
    // 启动倒计时
    startTimer();
  } else {
    router.push("/"); // 如果数据无效,返回首页
  }
});

const handleBack = () => {
  router.back();
};

const handleOpen = async () => {
  if (isAnimating.value) return;

  isAnimating.value = true;
  try {
    // 参与活动
    const response = await joinActivity({
      user_id: store.state.auth.user.id,
      activity_id: activityData.value?.id || 0,
    });

    if (response) {
      activityStatus.value = {
        ...response,
        ...response.record,
      };
      showSuccess("Successfully joined the activity!");
      // 显示结果
      status.value = "help";
    }
  } catch (error: any) {
    console.error("Failed to join activity:", error);
    showError(error.message || "Failed to join activity");
  } finally {
    isAnimating.value = false;
  }
};

const handleInvite = async () => {
  if (activityStatus.value?.invite_link) {
    // 生成分享链接
    const shareUrl = `${window.location.origin}/s/${activityStatus.value.invite_link}`;
    // 复制链接到剪贴板
    const success = await copyToClipboard(shareUrl);
    if (success) {
      showSuccess("Link copied to clipboard!");
    } else {
      showError("Failed to copy link");
    }
  }
};

const handleShare = async () => {
  if (activityStatus.value?.target_amount) {
    // 完整的分享链接
    const appUrl = window.location.origin; // 替换为实际的网站地址
    const shareLink = `${appUrl}/s/${activityStatus.value.invite_link}`;

    // 分享文本（葡萄牙语）
    const shareText = `Participei de uma atividade na BOX777 e recebi com sucesso ${activityStatus.value.target_amount} moedas! 🎉`;

    // 使用 Telegram 分享
    const telegramUrl = `https://t.me/share/url?text=${encodeURIComponent(
      shareText
    )}&url=${encodeURIComponent(shareLink)}`;

    // 打开 Telegram 分享页面
    window.location.href = telegramUrl;
  }
};

const formatTime = (ms: number) => {
  const hours = Math.floor(ms / 3600000);
  const minutes = Math.floor((ms % 3600000) / 60000);
  const seconds = Math.floor((ms % 60000) / 1000);
  return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

// Start countdown timer
let timer: number;

const startTimer = () => {
  timer = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value -= 1000;
    } else {
      window.clearInterval(timer);
    }
  }, 1000);
};

onUnmounted(() => {
  if (timer) {
    window.clearInterval(timer);
  }
  // 恢复页面背景色
  document.body.style.backgroundColor = "";
  // 恢复viewport设置
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute("content", "width=device-width, initial-scale=1");
  }
});
</script>

<style lang="scss" scoped>
.help {
  background-size: 100% auto !important;
}
.help-box {
  bottom: 14vh !important;
}
.pdd-activity {
  .blck-bg {
    background: rgba(0, 0, 0, 0.8);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  min-height: 100vh;
  background: url("@/assets/images/h5/pdd-bg.png") no-repeat center center;
  background-size: 100% 100%;
  padding: 20px;
  position: relative;
  overflow: hidden;
  // 添加安全区域padding
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }

  .back-button {
    position: fixed;
    top: max(env(safe-area-inset-top), 16px);
    left: 16px;
    z-index: 100;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;

    &:active {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  .reward-card {
    position: absolute;
    z-index: 2;
    width: 80%;
    max-width: 340px;
    background: transparent;
    border-radius: 12px;
    left: 10%;
    padding: 30px 12px;
    bottom: 22vh;
    text-align: center;
    color: #fff;
    .reward-title-tip {
      font-size: 0.8rem;
      margin-bottom: 10px;
      font-family: FZLanTingHeiDB-SC;
      font-weight: 400;
      color: #f74644;
    }
    // 未开启状态的背景
    &:not(.opened) {
      background: linear-gradient(1deg, #facc2e, #fdf6c7);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
        0 20px 40px rgba(250, 204, 46, 0.15),
        0 -2px 6px rgba(255, 255, 255, 0.3) inset,
        0 2px 4px rgba(0, 0, 0, 0.1) inset;
      background-size: 100% 100%;
      aspect-ratio: 1.2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transform: translateY(-5px);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      &:active {
        transform: translateY(0);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15),
          0 10px 20px rgba(250, 204, 46, 0.1),
          0 -2px 6px rgba(255, 255, 255, 0.3) inset,
          0 2px 4px rgba(0, 0, 0, 0.1) inset;
      }

      .reward-amount {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 30px;
        font-family: Alibaba PuHuiTi;
        font-weight: 800;
        color: #f74644;
      }

      .open-button {
        width: 90px;
        height: 90px;
        background: url("@/assets/images/h5/pdd-open-btn.png") no-repeat center
          center;
        background-size: contain;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;

        span {
          opacity: 1;
          font-size: 1.2rem;
          font-weight: 1000;
          text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
        }
      }

      .triangle {
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #fee2a2;
        margin: 14px auto 0;
      }

      .open-tip {
        font-size: 13px;
        color: #f74644;
        background: #fee2a2;
        border-radius: 6px;
        padding: 4px 6px;
        display: inline-block;
        font-weight: 600;
      }
    }

    // 已开启状态的背景
    &.opened {
      background: linear-gradient(180deg, #ffb258 0%, #ff8a00 100%);
    }

    .reward-header {
      font-size: 40px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .help-text {
      font-size: 16px;
      margin-bottom: 12px;
    }

    .countdown {
      font-size: 14px;
      margin-bottom: 16px;
      color: rgba(255, 255, 255, 0.8);
    }

    .invite-btn {
      background: #ff4444 !important;
      color: #fff;
      border-radius: 24px;
      padding: 0 32px;
      height: 48px;
      font-size: 18px;
      text-transform: none;
      margin-top: 16px;
    }

    .congrats-amount {
      font-size: 48px;
      font-weight: bold;
      margin: 20px 0;
    }

    .congrats-text,
    .final-text {
      font-size: 18px;
      margin-bottom: 12px;
    }

    .bonus-text {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 20px;
    }

    &.shake {
      animation: shakeCard 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
    }
  }

  .reward-card-help {
    padding: 30px 12px;
    padding-top: 80px;
    position: absolute;
    z-index: 2;
    width: 80%;
    max-width: 340px;
    background: transparent;
    border-radius: 12px;
    left: 10%;
    bottom: 14vh;
    color: #fff;
    // 未开启状态的背景
    &:not(.opened) {
      background: linear-gradient(1deg, #facc2e, #fdf6c7);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
        0 20px 40px rgba(250, 204, 46, 0.15),
        0 -2px 6px rgba(255, 255, 255, 0.3) inset,
        0 2px 4px rgba(0, 0, 0, 0.1) inset;
      background-size: 100% 100%;
      aspect-ratio: 1.2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transform: translateY(-5px);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      &:active {
        transform: translateY(0);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15),
          0 10px 20px rgba(250, 204, 46, 0.1),
          0 -2px 6px rgba(255, 255, 255, 0.3) inset,
          0 2px 4px rgba(0, 0, 0, 0.1) inset;
      }
    }
    .help-text {
      font-family: FZLanTingHei-B-GBK;
      font-weight: 400;
      font-size: 0.6rem;
      color: #f74644;
      padding: 6px 10px;
    }
    .final-text {
      background: #fef2bd;
      border-radius: 8px;
      padding: 10px;
      .final-text-title {
        font-family: FZDaHei-B02S;
        font-weight: 400;
        font-size: 1rem;
        color: #f74644;
        line-height: 1rem;
      }
      font-family: FZLanTingHeiDB-SC;
      font-weight: 400;
      font-size: 0.6rem;
      color: #f74644;
      line-height: 1rem;
    }
    .share-btn {
      background: url("@/assets/images/h5/help-btn.png") no-repeat center center;
      background-size: 100% 100%;
      height: 40px;
      box-shadow: none;
      border-radius: 20px;
    }
    .countdown {
      padding: 8px 0;
      font-family: FZLanTingHei-B-GBK;
      font-weight: 400;
      font-size: 0.7rem;
      color: #f74644;
    }
    .reward-header {
      width: 90%;
      position: absolute;
      top: -50px;
      height: 120px;
      text-align: center;
      overflow: hidden;
      border-radius: 12px;

      .header-main {
        width: calc(100% - 40px);
        border: 4px #faab59 solid;
        margin-left: 20px;
        border-bottom: 0;
        height: auto;
        z-index: 2;
        height: 100px;
        border-radius: 12px 12px 0 0;
        .amount-text {
          font-size: 42px;
          font-family: Alibaba PuHuiTi;
          font-weight: 800;
          color: #ffffff;
          padding: 4px 0;
          span {
            font-size: 22px;
          }
        }
        .progress-bar {
          width: calc(100% - 16px);
          margin-left: 8px;
          overflow: inherit;
          :deep(.v-progress-linear__background) {
            background: #be3b16 !important;
            opacity: 1 !important;
            border-radius: 5px;
          }
          :deep(.v-progress-linear__determinate) {
            border-radius: 5px !important;
            &::after {
              content: "R$";
              position: absolute;
              display: block;
              background: url("@/assets/images/h5/progress-icon-bg.png")
                no-repeat center;
              background-size: 100% 100%;
              width: 21px;
              height: 21px;
              right: -10.5px;
              top: -6px;
              z-index: 9999;
              color: #fff;
              font-size: 0.7rem;
              font-weight: 800;
              line-height: 21px;
              text-align: center;
            }
          }
        }
      }
      .header-radius {
        display: block;
        height: 120px;
        width: calc(100% + 40px); // 宽度稍大一些使弧度更自然
        background: #ec700a;
        position: absolute;
        bottom: 0px;
        border-radius: 8px 8px 50% 50%; // 使用百分比控制弧度
        overflow: hidden;
        left: -20px;
      }
    }
  }
  .card-ok {
    bottom: 22vh;
    .ok-img {
      position: absolute;
      top: -220px;
      z-index: 999;
      width: 300px;
      height: 300px;
      overflow: hidden;
      left: calc(50% - 150px);
      text-align: center;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("@/assets/images/h5/ok-img-bg.png") no-repeat center;
        background-size: 100% 100%;
        background-position: top;
        animation: rotateBackground 8s linear infinite;
        z-index: -2;
      }

      img {
        margin-top: 60px;
        width: 160px;
        position: relative;
        z-index: 1;
      }
    }
    .reward-header {
      height: 160px;
      top: -90px;
      .header-radius {
        height: 160px;
      }
      .header-main {
        height: 160px;
      }
      .amount-text {
        margin-top: 60px;
        font-size: 60px !important;
      }
    }
    .triangle {
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #fee2a2;
      margin: 0 auto 14px;
    }
  }
  .reward-box {
    position: absolute;
    overflow: hidden;
    width: 100%;
    height: auto;
    bottom: 22vh;
    height: 180px;
    right: -45px;
    z-index: 1;
    background: url("@/assets/images/h5/bg-box.png") no-repeat center bottom;
    background-size: 100% auto;
    margin-bottom: 6px;
  }

  .rules-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;

    h3 {
      color: #333;
      font-size: 16px;
      margin-bottom: 12px;
    }

    ol {
      padding-left: 20px;

      li {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.4;
      }
    }
  }
}

@keyframes shakeCard {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }
  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.coin-burst-layer {
  position: fixed;
  left: 50%;
  bottom: 40%;
  transform: translateX(-50%);
  width: 1px;
  height: 1px;
  pointer-events: none;
  z-index: 1000;

  .coin {
    position: absolute;
    width: 40px;
    height: 40px;
    animation: coinBurst 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    animation-delay: var(--delay);

    .coin-inner {
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 30%, #ffd700, #ffa500);
      border-radius: 50%;
      border: 2px solid #daa520;
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
      animation: coinRotate 0.6s linear infinite;

      &::after {
        content: "$";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #8b4513;
        font-weight: bold;
        font-size: 20px;
      }
    }
  }
}

@keyframes coinBurst {
  0% {
    transform: translate(0, 0) rotate(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translate(0, 0) rotate(0) scale(var(--scale));
  }
  100% {
    transform: translate(var(--x), var(--y)) rotate(var(--rotate))
      scale(var(--scale));
    opacity: 0;
  }
}

@keyframes coinRotate {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

@keyframes rotateBackground {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
