<script setup lang="ts">
import { ref, reactive } from 'vue';
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElInput,
  ElPagination,
} from 'element-plus';
import { useTable } from '@/hooks/common/table';
import { getWalletReport, type WalletReportParams, type WalletReportRecord } from '@/service/api/wallet';
import { getDepositWithdrawalRanking, type DepositWithdrawalRankingRecord } from '@/service/api/wallet';

interface SearchParams {
  userId?: string;
  phone?: string;
  dateRange?: [Date, Date];
  user_id?: string;
  action_type?: string;
  balance_type?: string;
  min_amount?: number;
  max_amount?: number;
  transaction_id?: string;
  related_id?: string;
  admin_id?: string;
}

interface UserDynamics {
  date: string;
  userId: string;
  phone: string;
  realNameStatus: string;
  purchaseStatus: string;
  inviteCount: number;
  rechargeTotal: number;
  inviteTotal: number;
  purchaseTotal: number;
  withdrawalTotal: number;
  platformProfit: number;
}

// 搜索参数
const searchParams = reactive<SearchParams>({});

// 分页参数
const page = ref(1);
const pageSize = ref(15);
const total = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref<UserDynamics[]>([]);

const columns = () => [
  { prop: 'index', label: '序号', width: 64 },
  { prop: 'user_id', label: '用户ID', minWidth: 100 },
  { prop: 'deposit_amount', label: '充值金额', minWidth: 120, formatter: (row: DepositWithdrawalRankingRecord) => (row.deposit_amount / 100).toFixed(2) },
  { prop: 'withdrawal_amount', label: '提现金额', minWidth: 120, formatter: (row: DepositWithdrawalRankingRecord) => (row.withdrawal_amount / 100).toFixed(2) },
  { prop: 'diff_amount', label: '充提差额', minWidth: 120, formatter: (row: DepositWithdrawalRankingRecord) => (row.diff_amount / 100).toFixed(2) },
  { prop: 'deposit_count', label: '充值次数', minWidth: 100 },
  { prop: 'withdrawal_count', label: '提现次数', minWidth: 100 }
];

const {
  columns: tableColumns,
  data,
  loading: tableLoading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams: tableSearchParams,
  updateSearchParams,
  resetSearchParams
} = useTable({
  apiFn: getDepositWithdrawalRanking,
  apiParams: { page: 1, size: 20, sort_by: 'deposit_amount', sort_order: 'desc' },
  columns,
  immediate: true
});

// 日期区间辅助
const dateRange = ref<[string, string] | undefined>();

function handleSearch() {
  tableSearchParams.user_id = searchParams.user_id;
  if (dateRange.value) {
    tableSearchParams.start_time = dateRange.value[0] ? new Date(dateRange.value[0]).getTime() : undefined;
    tableSearchParams.end_time = dateRange.value[1] ? new Date(dateRange.value[1]).getTime() : undefined;
  } else {
    tableSearchParams.start_time = undefined;
    tableSearchParams.end_time = undefined;
  }
  getData();
}

function handleReset() {
  resetSearchParams();
  dateRange.value = undefined;
  tableSearchParams.start_time = undefined;
  tableSearchParams.end_time = undefined;
  getData();
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getData();
};

// 页码变化
const handleCurrentChange = (val: number) => {
  page.value = val;
  getData();
};

// 初始化
getData();

defineOptions({ name: 'UserAccountDynamics' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>用户账户动态</p>
          <TableHeaderOperation @refresh="getData" ><span style="width: 1px;height: 35px;background: #e5e6eb;"></span></TableHeaderOperation>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="tableLoading"
          height="100%"
          :data="data"
        >
          <ElTableColumn prop="index" label="序号" width="64" />
          <ElTableColumn prop="user_id" label="用户ID" min-width="100" />
          <ElTableColumn prop="deposit_amount" label="充值金额" min-width="120">
            <template #default="{ row }">
              {{ (row.deposit_amount / 100).toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="withdrawal_amount" label="提现金额" min-width="120">
            <template #default="{ row }">
              {{ (row.withdrawal_amount / 100).toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="diff_amount" label="充提差额" min-width="120">
            <template #default="{ row }">
              {{ (row.diff_amount / 100).toFixed(2) }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="deposit_count" label="充值次数" min-width="100" />
          <ElTableColumn prop="withdrawal_count" label="提现次数" min-width="100" />
        </ElTable>

        <div class="mt-20px flex justify-between items-center">
          <div class="flex gap-24px text-14px">
            <span>总记录数：<span class="text-primary">{{ mobilePagination.total }}</span></span>
          </div>
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius:4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select {
        width: 160px;
      }

      .el-date-editor {
        width: 260px;
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-primary {
  color: var(--el-color-primary);
}
</style>
