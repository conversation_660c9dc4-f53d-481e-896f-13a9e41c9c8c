<script setup lang="ts">
import { ref, computed, watch } from "vue";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElMessage,
  ElSwitch,
  ElRadioGroup,
  ElRadio,
  ElCheckboxGroup,
  ElCheckbox,
  ElTabs,
  ElTabPane,
  ElDialog,
} from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import ImageUpload from "@/components/upload/ImageUpload.vue";
import { useI18n } from "vue-i18n";
// 引入获取、新增和更新活动的API（待实现）
import {
  getPddUserActivityDetail,
  addPddUserActivity,
  updatePddUserActivity,
} from "@/service/api/pdd";

// 定义其他奖励列表项的数据接口
interface RewardListItem {
  start_with?: number;
  end_with?: number;
  probability?: number;
}

// 定义活动表单的数据接口
interface PddActivityFormItem {
  activity_rules: string;
  progress: number[];
  user_type?: number;
  register_num?: number;
  register_completion_probability?: number;
  recharge_first_num_finish?: number;
  register_num_finish?: number;
  progress_finish?: number;
  must_recharge_amount?: number;
  countdown_time?: number;
  status?: number;
  bonus_type?: number;
  bonus_amount?: number;
  tg_link?: string;
  file_path?: string;
  share_copywriting?: string;
  other_recharge_reward: {
    first_invited: boolean;
    first_recharge: boolean;
    invited: boolean;
    invited_recharge: boolean;
    first_invited_list: RewardListItem[];
    first_recharge_list: RewardListItem[];
    invited_list: RewardListItem[];
    invited_recharge_list: RewardListItem[];
  };
}

const { t } = useI18n();

interface Props {
  visible: boolean;
  mode: "add" | "edit" | "view";
  activityId?: number;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);
const formLoading = ref(false);
const countdownUnit = ref("second");

const countdownUnitOptions = [
  { label: "时", value: "hour" },
  { label: "分", value: "minute" },
  { label: "秒", value: "second" },
];

const formData = ref<PddActivityFormItem>({
  activity_rules: "",
  progress: [],
  user_type: undefined,
  register_num: undefined,
  register_completion_probability: undefined,
  recharge_first_num_finish: undefined,
  register_num_finish: undefined,
  progress_finish: undefined,
  must_recharge_amount: undefined,
  countdown_time: undefined,
  status: undefined,
  bonus_type: undefined,
  bonus_amount: undefined,
  tg_link: "",
  file_path: "",
  share_copywriting: "",
  other_recharge_reward: {
    first_invited: false,
    first_recharge: false,
    invited: false,
    invited_recharge: false,
    first_invited_list: [
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
    ],
    first_recharge_list: [
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
    ],
    invited_list: [
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
    ],
    invited_recharge_list: [
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
      { start_with: undefined, end_with: undefined, probability: undefined },
    ],
  },
});

const rules = computed<FormRules>(() => ({
  activity_rules: [
    {
      required: props.mode !== "view",
      message: "请输入活动规则",
      trigger: "blur",
    },
  ],
  progress: [
    {
      required: props.mode !== "view",
      message: "请设置进度",
      trigger: "change",
    },
    {
      validator: (rule, value, callback) => {
        if (!value || value.length !== 2) {
          callback(new Error("请设置有效的进度范围"));
        } else if (value[0] >= value[1]) {
          callback(new Error("最小值必须小于最大值"));
        } else if (value[0] < 0 || value[1] > 99.99) {
          callback(new Error("进度范围必须在0-99.99之间"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  user_type: [
    {
      required: props.mode !== "view",
      message: "请选择参加对象",
      trigger: "change",
    },
  ],
  register_num: [
    {
      required: props.mode !== "view",
      message: "请输入注册人数",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入注册人数"));
        } else if (value < 0) {
          callback(new Error("人数不能为负数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  register_num_finish: [
    {
      required: props.mode !== "view",
      message: "请输入注册人数",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入注册人数"));
        } else if (value <= 0) {
          callback(new Error("注册人数必须大于0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  progress_finish: [
    {
      required: props.mode !== "view",
      message: "请输入参加进度",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入参加进度"));
        } else if (value <= 0) {
          callback(new Error("参加进度必须大于0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  register_completion_probability: [
    {
      required: props.mode !== "view",
      message: "请输入完成后中奖概率增加值",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入完成后中奖概率增加值"));
        } else if (value < 0 || value > 100) {
          callback(new Error("概率增加值必须在0-100之间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  recharge_first_num_finish: [
    {
      required: props.mode !== "view",
      message: "请输入首充人数",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入首充人数"));
        } else if (value <= 0) {
          callback(new Error("首充人数必须大于0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  must_recharge_amount: [
    {
      required: props.mode !== "view",
      message: "请输入充值金额",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入充值金额"));
        } else if (value < 0) {
          callback(new Error("充值金额必须大于等于0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  countdown_time: [
    {
      required: props.mode !== "view",
      message: "请输入倒计时时间",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入倒计时时间"));
        } else if (value < 0) {
          callback(new Error("倒计时时间不能为负数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  bonus_type: [
    {
      required: props.mode !== "view",
      message: "请选择奖金类型",
      trigger: "change",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请选择奖金类型"));
        } else if (![1, 2, 3].includes(value)) {
          callback(new Error("奖金类型无效"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  bonus_amount: [
    {
      required: props.mode !== "view",
      message: "请输入奖金金额",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error("请输入奖金金额"));
        } else if (value < 0) {
          callback(new Error("奖金金额必须大于等于0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  tg_link: [
    {
      required: props.mode !== "view",
      message: "请输入 Telegram 群连结",
      trigger: "blur",
    },
    {
      type: "url",
      message: "请输入有效的 URL 格式",
      trigger: ["blur", "change"],
    },
  ],
  share_copywriting: [
    {
      required: props.mode !== "view",
      message: "请输入分享文案",
      trigger: "blur",
    },
    { max: 500, message: "长度不能超过500个字符", trigger: "blur" },
  ],
}));

const participantOptions = [
  { label: "全部用户", value: 1 },
  { label: "新用户", value: 2 },
  { label: "vip用户", value: 3 },
];

const multiplierOptions = [
  { label: "0.5", value: 0.5 },
  { label: "1", value: 1 },
  { label: "2", value: 2 },
  { label: "3", value: 3 },
];

const activeTab = ref("activity");

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.mode === "edit" && props.activityId) {
        fetchActivityDetail(props.activityId);
      } else {
        // 新增或查看模式，重置表单
        resetForm();
        if (props.mode === "view" && props.activityId) {
          fetchActivityDetail(props.activityId);
        }
      }
    } else {
      // 弹窗关闭时重置表单
      resetForm();
    }
    // 重置表单验证状态
    formRef.value?.clearValidate();
  },
);

const resetForm = () => {
  formRef.value?.resetFields();
  formData.value = {
    activity_rules: "",
    progress: [],
    user_type: undefined,
    register_num: undefined,
    register_completion_probability: undefined,
    recharge_first_num_finish: undefined,
    register_num_finish: undefined,
    progress_finish: undefined,
    must_recharge_amount: undefined,
    countdown_time: undefined,
    status: undefined,
    bonus_type: undefined,
    bonus_amount: undefined,
    tg_link: "",
    file_path: "",
    share_copywriting: "",
    other_recharge_reward: {
      first_invited: false,
      first_recharge: false,
      invited: false,
      invited_recharge: false,
      first_invited_list: [
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
      ],
      first_recharge_list: [
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
      ],
      invited_list: [
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
      ],
      invited_recharge_list: [
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
        { start_with: undefined, end_with: undefined, probability: undefined },
      ],
    },
  };
  otherRechargeReward.value = [];
  activeTab.value = "activity";
};

const fetchActivityDetail = async (id: number) => {
  loading.value = true;
  formLoading.value = true;
  try {
    const response = await getPddUserActivityDetail(id);
    console.log(response.data.data);
    if (response.data) {
      const activityData = response.data.data;

      // 处理 progress 字串转换为数组
      if (typeof activityData.progress === "string") {
        activityData.progress = JSON.parse(activityData.progress);
      }

      // 处理 other_recharge_reward 字串转换为对象
      if (typeof activityData.other_recharge_reward === "string") {
        activityData.other_recharge_reward = JSON.parse(
          activityData.other_recharge_reward,
        );
      }

      // 設置 otherRechargeReward 的值
      const rewardTypes = [];
      if (activityData.other_recharge_reward.first_recharge) {
        rewardTypes.push("firstRecharge");
      }
      if (activityData.other_recharge_reward.first_invited) {
        rewardTypes.push("firstInvitied");
      }
      if (activityData.other_recharge_reward.invited_recharge) {
        rewardTypes.push("invitedRecharge");
      }
      if (activityData.other_recharge_reward.invited) {
        rewardTypes.push("invited");
      }
      otherRechargeReward.value = rewardTypes;

      console.log(otherRechargeReward.value);
      Object.assign(formData.value, activityData);

      // 如果有倒计时数据，转换为合适的单位
      if (formData.value.countdown_time) {
        const timeResult = convertFromMilliseconds(
          formData.value.countdown_time,
        );
        formData.value.countdown_time = timeResult.value;
        countdownUnit.value = timeResult.unit;
      }
    }
  } catch (error) {
    console.error("获取活动详情失败:", error);
    ElMessage.error("获取活动详情失败");
  } finally {
    loading.value = false;
    formLoading.value = false;
  }
};

const handleClose = () => {
  // 重置表单验证状态
  formRef.value?.clearValidate();
  // 重置表单数据
  resetForm();
  // 关闭弹窗
  emit("update:visible", false);
};

const validateProbabilitySum = (list: { probability?: number | string }[]) => {
  const sum = list.reduce(
    (acc, curr) => acc + (Number(curr.probability) || 0),
    0,
  );
  return Math.abs(sum - 100) < 0.01;
};

const validateRewardRanges = (list: RewardListItem[]) => {
  // 檢查是否所有區間都已填寫
  const isComplete = list.every(
    (item) =>
      item.start_with !== undefined &&
      item.end_with !== undefined &&
      item.probability !== undefined,
  );

  if (!isComplete) {
    return false;
  }

  // 檢查區間是否有效
  const isValidRanges = list.every(
    (item) =>
      Number(item.start_with) >= 0 &&
      Number(item.end_with) > Number(item.start_with),
  );

  if (!isValidRanges) {
    return false;
  }

  // 檢查概率總和是否為100%
  return validateProbabilitySum(list);
};

const otherRechargeReward = ref<string[]>([]);

// 監聽 otherRechargeReward 的變化
watch(otherRechargeReward, (newValue) => {
  // 重置所有布爾值為 false
  formData.value.other_recharge_reward.first_recharge = false;
  formData.value.other_recharge_reward.first_invited = false;
  formData.value.other_recharge_reward.invited_recharge = false;
  formData.value.other_recharge_reward.invited = false;

  // 根據選中的值設置對應的布爾值為 true
  newValue.forEach((value) => {
    switch (value) {
      case "firstRecharge":
        formData.value.other_recharge_reward.first_recharge = true;
        break;
      case "firstInvitied":
        formData.value.other_recharge_reward.first_invited = true;
        break;
      case "invitedRecharge":
        formData.value.other_recharge_reward.invited_recharge = true;
        break;
      case "invited":
        formData.value.other_recharge_reward.invited = true;
        break;
    }
  });
});

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 检查概率总和和区间有效性
    if (otherRechargeReward.value.length > 0) {
      let currentList: RewardListItem[] = [];
      if (otherRechargeReward.value.includes("firstInvitied")) {
        currentList = formData.value.other_recharge_reward.first_invited_list;
      } else if (otherRechargeReward.value.includes("firstRecharge")) {
        currentList = formData.value.other_recharge_reward.first_recharge_list;
      } else if (otherRechargeReward.value.includes("invited")) {
        currentList = formData.value.other_recharge_reward.invited_list;
      } else if (otherRechargeReward.value.includes("invitedRecharge")) {
        currentList =
          formData.value.other_recharge_reward.invited_recharge_list;
      }

      if (currentList.length > 0) {
        if (!validateRewardRanges(currentList)) {
          ElMessage.error("请确保所有区间都已填写，且区间有效，概率总和为100%");
          return;
        }
      }
    }

    // 转换数据类型
    const submitData = { ...formData.value };

    // 转换倒计时为毫秒
    if (
      submitData.countdown_time !== undefined &&
      submitData.countdown_time !== null
    ) {
      submitData.countdown_time = convertToMilliseconds(
        Number(submitData.countdown_time),
        countdownUnit.value as TimeUnit,
      );
    }

    // 转换所有数字类型字段
    const numericFields: Array<keyof PddActivityFormItem> = [
      "user_type",
      "register_num",
      "register_completion_probability",
      "recharge_first_num_finish",
      "register_num_finish",
      "progress_finish",
      "must_recharge_amount",
      "status",
      "bonus_type",
      "bonus_amount",
    ];

    numericFields.forEach((field) => {
      const value = (submitData as Record<string, any>)[field];
      if (typeof value === "string") {
        (submitData as Record<string, any>)[field] =
          value === "" ? undefined : Number(value);
      } else if (value === null) {
        (submitData as Record<string, any>)[field] = undefined;
      }
    });

    // 转换 other_recharge_reward 中的数字字段
    const listKeys: Array<
      | "first_invited_list"
      | "first_recharge_list"
      | "invited_list"
      | "invited_recharge_list"
    > = [
      "first_invited_list",
      "first_recharge_list",
      "invited_list",
      "invited_recharge_list",
    ];
    listKeys.forEach((listKey) => {
      const list = submitData.other_recharge_reward[listKey];
      if (Array.isArray(list)) {
        list.forEach((item: RewardListItem) => {
          (
            ["start_with", "end_with", "probability"] as Array<
              keyof RewardListItem
            >
          ).forEach((key) => {
            const value = item[key];
            if (typeof value === "string") {
              item[key] = value === "" ? undefined : Number(value);
            } else if (value === null) {
              item[key] = undefined;
            }
          });
        });
      }
    });

    submitData.progress = submitData.progress.map(Number);

    loading.value = true;
    let res;
    if (props.mode === "add") {
      res = await addPddUserActivity(submitData);
      ElMessage.success("新增活动成功");
    } else if (props.mode === "edit") {
      res = await updatePddUserActivity(props.activityId!, submitData);
      ElMessage.success("更新活动成功");
    }
    if (res?.response?.status === 200) {
      emit("success");
      handleClose();
    }
  } catch (error) {
    // 处理表单校验失败
    if (error && typeof error === "object" && !Array.isArray(error)) {
      // 你的 error 就是一个字段名到校验信息数组的映射
      const messages: string[] = [];
      Object.values(error).forEach((fieldArr: any) => {
        if (Array.isArray(fieldArr)) {
          fieldArr.forEach((item) => {
            if (item && item.message) {
              messages.push(item.message);
            }
          });
        }
      });
      if (messages.length > 0) {
        ElMessage.error(messages.join("；"));
      } else {
        ElMessage.error("表单校验失败，请检查输入项");
      }
    } else {
      // 其他异常
      console.error("表单验证失败:", error);
      ElMessage.error("表单校验失败，请检查输入项");
    }
  } finally {
    loading.value = false;
  }
};

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增活动配置",
    edit: "编辑活动配置",
    view: "查看活动配置",
  };
  return titles[props.mode];
});

const isViewMode = computed(() => props.mode === "view");

// 定义时间单位类型
type TimeUnit = "hour" | "minute" | "second";

// 定义时间转换结果接口
interface TimeConversionResult {
  value: number;
  unit: TimeUnit;
}

// 将时间数字和单位转换为毫秒
const convertToMilliseconds = (value: number, unit: TimeUnit): number => {
  const multipliers = {
    hour: 3600000,
    minute: 60000,
    second: 1000,
  };
  return value * multipliers[unit];
};

// 将毫秒转换为最合适的时单位和数值
const convertFromMilliseconds = (
  milliseconds: number,
): TimeConversionResult => {
  const hour = 3600000;
  const minute = 60000;
  const second = 1000;

  if (milliseconds >= hour) {
    return {
      value: Math.floor(milliseconds / hour),
      unit: "hour",
    };
  } else if (milliseconds >= minute) {
    return {
      value: Math.floor(milliseconds / minute),
      unit: "minute",
    };
  } else {
    return {
      value: Math.floor(milliseconds / second),
      unit: "second",
    };
  }
};

defineExpose({
  formRef,
  formData,
  resetForm,
});
</script>

<template>
  <ElDialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="title"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    :destroy-on-close="true"
  >
    <div class="dialog-content" v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :disabled="props.mode === 'view' || loading"
      >
        <ElTabs v-model="activeTab">
          <ElTabPane label="活动内容" name="activity">
            <ElFormItem label="活动状态" prop="status">
              <ElSwitch
                v-model="formData.status"
                :disabled="isViewMode"
                :active-value="1"
                :inactive-value="2"
                active-text="启用"
                inactive-text="停用"
              />
            </ElFormItem>
            <ElFormItem label="活动规则" prop="activity_rules">
              <ElInput
                v-model="formData.activity_rules"
                type="textarea"
                :rows="3"
                placeholder="请输入活动规则"
                show-word-limit
                :readonly="isViewMode"
              />
            </ElFormItem>

            <ElFormItem
              label="参加进度(初始进
度:区间内随机)"
              prop="progress"
              class="progress-initial-item flex items-center"
            >
              <div class="flex items-center gap-5px">
                <ElInput
                  v-model="formData.progress[0]"
                  placeholder="最小值"
                  type="number"
                  :readonly="isViewMode"
                  class="fixed-width-input"
                  min="0"
                  max="100"
                >
                  <template #suffix> % </template>
                </ElInput>
                <span>~</span>
                <ElInput
                  v-model="formData.progress[1]"
                  placeholder="最大值"
                  type="number"
                  :readonly="isViewMode"
                  class="fixed-width-input"
                  min="0"
                  max="100"
                >
                  <template #suffix> % </template>
                </ElInput>
              </div>
              <div class="text-gray-500 text-sm mt-5px">
                (进度n-99%之前，每个用户+1:99%-99.9%:每个用户+0.1，最高进度99.9%停止增加，除非触发中奖)
              </div>
            </ElFormItem>

            <ElFormItem label="参加对象" prop="user_type">
              <ElSelect
                v-model="formData.user_type"
                placeholder="請選擇參加對象"
                :readonly="isViewMode"
              >
                <ElOption
                  v-for="item in participantOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>

            <!-- 充值完成条件 -->
            <ElFormItem label="充值完成条件" class="form-item-group">
              <div class="group-content">
                <ElFormItem
                  label="邀请成功注册人数"
                  prop="register_num"
                  label-width="150px"
                >
                  <ElInput
                    v-model="formData.register_num"
                    type="number"
                    :readonly="isViewMode"
                    min="0"
                  />
                </ElFormItem>
                <ElFormItem
                  label="充值金额"
                  prop="must_recharge_amount"
                  label-width="150px"
                >
                  <ElInput
                    v-model="formData.must_recharge_amount"
                    type="number"
                    :readonly="isViewMode"
                    min="0"
                  />
                </ElFormItem>
                <div class="text-gray-500 text-sm mt-5px">
                  （邀请完成后进度为100%，30分钟内充值金额达标后分享TG群可提现）
                </div>
              </div>
            </ElFormItem>

            <!-- 直接完成条件 -->
            <ElFormItem label="直接完成条件" class="form-item-group">
              <div class="group-content condition-group">
                <div class="condition-items-wrapper">
                  <div class="condition-item">
                    <ElFormItem
                      label="邀请成功首充人数"
                      prop="recharge_first_num_finish"
                      label-width="150px"
                    >
                      <ElInput
                        v-model="formData.recharge_first_num_finish"
                        type="number"
                        :readonly="isViewMode"
                        min="0"
                      />
                    </ElFormItem>
                  </div>
                  <div class="condition-separator">
                    <span class="separator-text">或</span>
                  </div>
                  <div class="condition-item flex-row">
                    <ElFormItem
                      label="邀请成功注册人数"
                      prop="register_num_finish"
                      label-width="150px"
                    >
                      <ElInput
                        v-model="formData.register_num_finish"
                        type="number"
                        :readonly="isViewMode"
                        min="0"
                      />
                    </ElFormItem>
                    <ElFormItem
                      label="达标后完成任务概率"
                      prop="register_completion_probability"
                      label-width="150px"
                      class="ml-10px"
                    >
                      <ElInput
                        v-model="formData.register_completion_probability"
                        type="number"
                        min="0"
                        max="100"
                        :readonly="isViewMode"
                        class="percentage-input"
                      >
                        <template #suffix> % </template>
                      </ElInput>
                    </ElFormItem>
                  </div>
                </div>
                <div class="text-gray-500 text-sm mt-5px">
                  （到达人数目标后有对应概率直接到达100%进度）分享TG群后可提现
                </div>
              </div>
            </ElFormItem>

            <ElFormItem
              label="倒计时"
              prop="countdown_time"
              class="flex items-center"
            >
              <div class="flex items-center gap-5px">
                <ElInput
                  v-model.number="formData.countdown_time"
                  type="number"
                  :readonly="isViewMode"
                  min="0"
                  class="countdown-input"
                />
                <ElSelect
                  v-model="countdownUnit"
                  :disabled="isViewMode"
                  class="countdown-unit-select"
                >
                  <ElOption
                    v-for="item in countdownUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </div>
              <div class="text-gray-500 text-sm mt-5px">
                (设置后用户开始活动后开始计时)
              </div>
            </ElFormItem>
          </ElTabPane>

          <ElTabPane label="奖金设置" name="bonus">
            <div class="bonus-settings">
              <ElFormItem label="奖金设置">
                <div class="flex flex-col w-full">
                  <div class="bonus-type-group">
                    <ElFormItem label="" prop="bonus_type">
                      <ElRadioGroup
                        v-model="formData.bonus_type"
                        class="mb-10px"
                      >
                        <ElRadio :label="1">现金</ElRadio>
                        <ElRadio :label="2">赠金</ElRadio>
                        <ElRadio :label="3">打码提现</ElRadio>
                      </ElRadioGroup>
                    </ElFormItem>
                  </div>
                  <div class="bonus-percentage-group">
                    <ElFormItem label="奖金金额" prop="bonus_amount">
                      <ElInput
                        v-model="formData.bonus_amount"
                        type="number"
                        :readonly="isViewMode"
                        min="0"
                      />
                    </ElFormItem>
                  </div>
                </div>
              </ElFormItem>

              <ElFormItem label="其他奖金">
                <div class="flex flex-col w-full">
                  <div class="bonus-radio-group">
                    <ElCheckboxGroup
                      v-model="otherRechargeReward"
                      class="mb-10px"
                    >
                      <ElCheckbox :label="'firstInvitied'"
                        >首次助力(未充值)</ElCheckbox
                      >
                      <ElCheckbox :label="'firstRecharge'"
                        >首次助力(已充值)</ElCheckbox
                      >
                      <ElCheckbox :label="'invited'">已助力(未充值)</ElCheckbox>
                      <ElCheckbox :label="'invitedRecharge'"
                        >已助力(已充值)</ElCheckbox
                      >
                    </ElCheckboxGroup>
                  </div>
                  <div
                    v-if="otherRechargeReward.includes('firstInvitied')"
                    class="bonus-ranges-section"
                  >
                    <div class="section-title">
                      首次助力(未充值) 奖励金额区间
                    </div>
                    <div
                      v-for="(range, index) in formData.other_recharge_reward
                        .first_invited_list"
                      :key="index"
                      class="range-group"
                    >
                      <div class="range-inputs">
                        <ElFormItem
                          label="金额区间"
                          :prop="`other_recharge_reward.first_invited_list[${index}].start_with`"
                          label-width="80px"
                        >
                          <ElInput
                            v-model="range.start_with"
                            type="number"
                            placeholder="最小值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                        <span>~</span>
                        <ElFormItem
                          :prop="`other_recharge_reward.first_invited_list[${index}].end_with`"
                          label-width="0px"
                        >
                          <ElInput
                            v-model="range.end_with"
                            type="number"
                            placeholder="最大值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                      </div>

                      <ElFormItem
                        label="获得概率"
                        label-width="80px"
                        :prop="`other_recharge_reward.first_invited_list[${index}].probability`"
                        class="probability-form-item"
                      >
                        <ElInput
                          v-model="range.probability"
                          type="number"
                          placeholder=""
                          :readonly="isViewMode"
                          min="0"
                          max="100"
                        >
                          <template #suffix> % </template>
                        </ElInput>
                      </ElFormItem>
                    </div>
                    <div class="text-gray-500 text-sm">
                      (三项概率之和为100%)
                    </div>
                  </div>

                  <div
                    v-if="otherRechargeReward.includes('firstRecharge')"
                    class="bonus-ranges-section"
                  >
                    <div class="section-title">
                      首次助力(已充值) 奖励金额区间
                    </div>
                    <div
                      v-for="(range, index) in formData.other_recharge_reward
                        .first_recharge_list"
                      :key="index"
                      class="range-group"
                    >
                      <div class="range-inputs">
                        <ElFormItem
                          label="金额区间"
                          :prop="`other_recharge_reward.first_recharge_list[${index}].start_with`"
                          label-width="80px"
                        >
                          <ElInput
                            v-model="range.start_with"
                            type="number"
                            placeholder="最小值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                        <span>~</span>
                        <ElFormItem
                          :prop="`other_recharge_reward.first_recharge_list[${index}].end_with`"
                          label-width="0px"
                        >
                          <ElInput
                            v-model="range.end_with"
                            type="number"
                            placeholder="最大值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                      </div>

                      <ElFormItem
                        label="获得概率"
                        label-width="80px"
                        :prop="`other_recharge_reward.first_recharge_list[${index}].probability`"
                        class="probability-form-item"
                      >
                        <ElInput
                          v-model="range.probability"
                          type="number"
                          placeholder=""
                          :readonly="isViewMode"
                          min="0"
                          max="100"
                        >
                          <template #suffix> % </template>
                        </ElInput>
                      </ElFormItem>
                    </div>
                    <div class="text-gray-500 text-sm">
                      (三项概率之和为100%)
                    </div>
                  </div>

                  <div
                    v-if="otherRechargeReward.includes('invited')"
                    class="bonus-ranges-section"
                  >
                    <div class="section-title">已助力(未充值) 奖励金额区间</div>
                    <div
                      v-for="(range, index) in formData.other_recharge_reward
                        .invited_list"
                      :key="index"
                      class="range-group"
                    >
                      <div class="range-inputs">
                        <ElFormItem
                          label="金额区间"
                          :prop="`other_recharge_reward.invited_list[${index}].start_with`"
                          label-width="80px"
                        >
                          <ElInput
                            v-model="range.start_with"
                            type="number"
                            placeholder="最小值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                        <span>~</span>
                        <ElFormItem
                          :prop="`other_recharge_reward.invited_list[${index}].end_with`"
                          label-width="0px"
                        >
                          <ElInput
                            v-model="range.end_with"
                            type="number"
                            placeholder="最大值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                      </div>

                      <ElFormItem
                        label="获得概率"
                        label-width="80px"
                        :prop="`other_recharge_reward.invited_list[${index}].probability`"
                        class="probability-form-item"
                      >
                        <ElInput
                          v-model="range.probability"
                          type="number"
                          placeholder=""
                          :readonly="isViewMode"
                          min="0"
                          max="100"
                        >
                          <template #suffix> % </template>
                        </ElInput>
                      </ElFormItem>
                    </div>
                    <div class="text-gray-500 text-sm">
                      (三项概率之和为100%)
                    </div>
                  </div>

                  <div
                    v-if="otherRechargeReward.includes('invitedRecharge')"
                    class="bonus-ranges-section"
                  >
                    <div class="section-title">已助力(已充值) 奖励金额区间</div>
                    <div
                      v-for="(range, index) in formData.other_recharge_reward
                        .invited_recharge_list"
                      :key="index"
                      class="range-group"
                    >
                      <div class="range-inputs">
                        <ElFormItem
                          label="金额区间"
                          :prop="`other_recharge_reward.invited_recharge_list[${index}].start_with`"
                          label-width="80px"
                        >
                          <ElInput
                            v-model="range.start_with"
                            type="number"
                            placeholder="最小值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                        <span>~</span>
                        <ElFormItem
                          :prop="`other_recharge_reward.invited_recharge_list[${index}].end_with`"
                          label-width="0px"
                        >
                          <ElInput
                            v-model="range.end_with"
                            type="number"
                            placeholder="最大值"
                            :readonly="isViewMode"
                            class="fixed-width-input"
                          />
                        </ElFormItem>
                      </div>

                      <ElFormItem
                        label="获得概率"
                        label-width="80px"
                        :prop="`other_recharge_reward.invited_recharge_list[${index}].probability`"
                        class="probability-form-item"
                      >
                        <ElInput
                          v-model="range.probability"
                          type="number"
                          placeholder=""
                          :readonly="isViewMode"
                          min="0"
                          max="100"
                        >
                          <template #suffix> % </template>
                        </ElInput>
                      </ElFormItem>
                    </div>
                    <div class="text-gray-500 text-sm">
                      (三项概率之和为100%)
                    </div>
                  </div>
                  <div class="text-gray-500 text-sm mt-5px">
                    (赠金，助力过程中发放给发起人和助力人的奖金，两人得奖金额相同)
                  </div>
                </div>
              </ElFormItem>
            </div>
          </ElTabPane>

          <ElTabPane label="分享设置" name="share">
            <ElFormItem label="Telegram群链接" prop="tg_link">
              <ElInput
                v-model="formData.tg_link"
                placeholder="https://t.me/your_group_link"
                :readonly="isViewMode"
              />
            </ElFormItem>

            <ElFormItem label="分享图片" prop="file_path">
              <ImageUpload
                v-model="formData.file_path"
                :max-size="20"
                :show-tip="true"
                tip-text="支持 jpg、png 格式图片，大小不超过 20MB"
              />
            </ElFormItem>

            <ElFormItem label="分享文案" prop="share_copywriting">
              <ElInput
                v-model="formData.share_copywriting"
                type="textarea"
                :rows="3"
                placeholder="请输入分享文案（最多500字）"
                maxlength="500"
                show-word-limit
                :readonly="isViewMode"
              />
            </ElFormItem>
            <div class="text-gray-500 text-sm mt-5px">
              (用户完成充值目标后，提现前需分享到Telegram)
            </div>
          </ElTabPane>
        </ElTabs>
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose" :disabled="loading">取消</ElButton>
        <ElButton
          v-if="props.mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-form) {
  .el-form-item {
    margin-bottom: 24px;

    .el-form-item__content {
      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  .progress-initial-item {
    .el-form-item__content {
      display: flex;
      align-items: center;
      gap: 5px;

      .el-input {
        width: 100px !important;
        flex: none;
      }
    }
  }
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-5px {
  gap: 5px;
}

.gap-10px {
  gap: 10px;
}

.mb-10px {
  margin-bottom: 10px;
}

.text-gray-500 {
  color: #999;
}

.text-sm {
  font-size: 14px;
}

.mt-5px {
  margin-top: 5px;
}

:deep(.el-dialog) {
  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100px;
  flex: none;
}

:deep(.el-textarea__inner) {
  min-height: 80px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}

.percentage-input {
  width: 100%;
  flex: none;
}

:deep(.el-select.multiplier-select) {
  width: 100px;
  flex: none;
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}

.fixed-width-input {
  width: 100px !important;
  flex: none !important;
}

.countdown-input {
  width: 120px !important;
  flex: none !important;
}

.countdown-unit-select {
  width: 80px !important;
  flex: none !important;
}

:deep(.fixed-width-input) {
  .el-input__wrapper {
    width: 100px !important;
  }
}

// 新增奖金设置相关样式
:deep(.bonus-settings) {
  .el-radio-group {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .bonus-percentage-group {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .el-form-item {
      margin-bottom: 0;
      flex: 1;
    }
  }

  .bonus-ranges-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .section-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 15px;
      font-weight: 500;
    }

    .range-group {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      flex-wrap: wrap;

      &:last-child {
        margin-bottom: 0;
      }

      .range-inputs {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1 1 250px;

        .el-form-item {
          margin-bottom: 0;
          flex: 1;

          .el-form-item__content {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
          }

          .el-input {
            flex-grow: 1;
            flex-shrink: 1;
            width: auto;
          }
        }

        .fixed-width-input {
          width: auto !important;
          flex-grow: 1;
        }
      }

      .probability-form-item {
        flex: 0 1 180px;
        display: flex;
        align-items: center;

        .el-form-item__label {
          flex-shrink: 0;
        }

        .el-form-item__content {
          flex: 1;
          min-width: 0;
          display: flex;
          align-items: center;
        }

        .el-input {
          flex-grow: 1;
          flex-shrink: 1;
        }
      }
    }
  }
}

:deep(.el-radio) {
  margin-right: 20px;

  &:last-child {
    margin-right: 0;
  }
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;

  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc inset;
  }

  &.is-focus {
    box-shadow: 0 0 0 1px #409eff inset;
  }
}

.bonus-radio-group {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .el-checkbox-group {
    margin-bottom: 0;
    padding: 0;
    background-color: transparent;
  }
}

:deep(.el-checkbox) {
  margin-right: 20px;

  &:last-child {
    margin-right: 0;
  }
}

/* 新增的样式 */
.form-item-group {
  .group-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    gap: 10px;

    .probability-items-wrapper {
      display: flex;
      align-items: center;
      gap: 20px;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .el-form-item {
        flex: 1;
        margin-bottom: 0;
        min-width: 0;

        .el-form-item__content {
          width: 100%;
        }

        .el-input {
          width: 100%;
        }
      }
    }
  }

  .condition-group {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .condition-items-wrapper {
      display: flex;
      flex-direction: column;
      // align-items: center;
      gap: 15px;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .condition-item {
      flex: 1;
      min-width: 0;

      .el-form-item {
        margin-bottom: 0;
      }

      .el-input {
        width: 100%;
      }
    }

    .condition-separator {
      flex-shrink: 0;
      padding: 0 5px;

      .separator-text {
        color: #909399;
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }
}

.flex-row {
  display: flex;
  align-items: center;
  gap: 20px;
}
.ml-10px {
  margin-left: 10px;
}

.share-image-upload-placeholder {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: #8c939d;

  &:hover {
    border-color: #409eff;
  }
}
</style>
