<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 15:03:40
 * @LastEditors: As<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-19 16:24:32
 * @FilePath: \betdoce-admin\src\views\account\banklist\modules\bank-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: Asad<PERSON>cj <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-16 14:54:27
 * @FilePath: \betdoce-admin\src\views\account\accountlist\modules\account-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="search-wrapper">
    <ElForm :model="model" inline>
      <ElRow :gutter="16" class="w-full">
        <ElCol :span="3">
          <ElFormItem label="用户ID">
            <ElInput v-model="model.user_id" placeholder="请输入用户ID" clearable />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="手机号">
            <ElInput v-model="model.phone" placeholder="请输入手机号" clearable />
          </ElFormItem>
        </ElCol>
        <ElCol :span="3">
          <ElFormItem label="卡号">
            <ElInput v-model="model.card_number" placeholder="请输入卡号" clearable />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="CPF">
            <ElInput v-model="model.cpf" placeholder="请输入CPF" clearable />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="创建时间">
            <ElDatePicker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              @change="
                  (val: [string, string] | null) => {
                    if (val) {
                      model.start_time = val[0];
                      model.end_time = val[1];
                    } else {
                      model.start_time = undefined;
                      model.end_time = undefined;
                    }
                  }
                "
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="$emit('search')">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <slot name="table-operation"></slot>
  </div>
</template>

<script setup lang="ts">
import { ElForm, ElFormItem, ElInput, ElButton, ElDatePicker, ElRow, ElCol } from 'element-plus';
import { ref } from 'vue';

const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

defineProps<{
  model: {
    user_id?: string;
    phone?: string;
    card_number?: string;
    cpf?: string;
  };
}>();

const emit = defineEmits<{
  (e: 'update:model', value: any): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}>();


const dateRange = ref()

const handleReset =()=>{
  dateRange.value = undefined
  emit('reset')
}


</script>

<style lang="scss" scoped>
.search-wrapper {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;

  :deep(.el-form) {
    width: 100%;

    .el-form-item {
      margin-bottom: 0;
      margin-right: 0;
      width: 100%;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .el-input__wrapper,
      .el-date-editor {
        width: 100%;
      }
    }

    .el-button {
      padding: 8px 20px;
      font-weight: 500;

      & + .el-button {
        margin-left: 12px;
      }
    }
  }
}
</style>
